# Song Registry Flask DApp

A simple Flask web application that interacts with the Song Registry smart contract on the blockchain.

## What This Demonstrates

This minimal Flask app shows you how to:
- Connect a web application to a blockchain
- Read data from a smart contract
- Build transactions to write data to a smart contract
- Display blockchain data in a web interface

## Setup Instructions

### 1. Deploy Your Smart Contract

First, make sure your smart contract is deployed:

```bash
cd thirdProject
truffle migrate
```

Note the deployed contract address from the output.

### 2. Update Configuration

Edit `app.py` and update these variables:

```python
CONTRACT_ADDRESS = '0x...'  # Replace with your deployed contract address
DEFAULT_ACCOUNT = '0x...'   # Replace with your Ganache account address
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Start Ganache

Make sure Ganache is running on `http://127.0.0.1:7545`

### 5. Run the Flask App

```bash
python app.py
```

Visit `http://127.0.0.1:5000` in your browser.

## How It Works

### Reading from Blockchain
- The app connects to Ganache using Web3.py
- It calls the `getNumberOfSongs()` and `songs()` functions to read data
- Data is displayed in simple HTML templates

### Writing to Blockchain
- The app builds transactions using Web3.py
- In this demo, transactions are built but not signed/sent
- In production, you'd integrate with MetaMask or use private keys

## Key Learning Points

1. **Web3 Connection**: `w3 = Web3(Web3.HTTPProvider('http://127.0.0.1:7545'))`
2. **Contract Instance**: `contract = w3.eth.contract(address=CONTRACT_ADDRESS, abi=CONTRACT_ABI)`
3. **Reading Data**: `contract.functions.getNumberOfSongs().call()`
4. **Building Transactions**: `contract.functions.registerSong(...).build_transaction(...)`
5. **Unit Conversion**: `w3.to_wei()` and `w3.from_wei()` for ETH/Wei conversion

## File Structure

```
├── app.py                 # Main Flask application
├── templates/
│   ├── base.html         # Base template with styling
│   ├── index.html        # Home page showing all songs
│   └── add_song.html     # Form to add new songs
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Next Steps

To make this fully functional:
1. Add proper transaction signing with private keys or MetaMask integration
2. Add error handling for blockchain connection issues
3. Add form validation
4. Add the ability to buy songs
5. Add user authentication

This is a minimal example to help you understand the basics of connecting web apps to blockchain!
