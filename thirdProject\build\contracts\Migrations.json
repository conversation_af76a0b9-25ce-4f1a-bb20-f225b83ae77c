{"contractName": "Migrations", "abi": [{"inputs": [], "name": "last_completed_migration", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "completed", "type": "uint256"}], "name": "setCompleted", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.8.21+commit.d9974bed\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"last_completed_migration\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"completed\",\"type\":\"uint256\"}],\"name\":\"setCompleted\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"project:/contracts/Migrations.sol\":\"Migrations\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/Migrations.sol\":{\"keccak256\":\"0xb5806dfb94b7e111177b0c88f11a3ca041004e8e1c83d46fb781f3df0ea20672\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1519a9c3ecf126400b366104cddbd2513c92f0b34a9090a7d5413d990ba4c5a5\",\"dweb:/ipfs/QmT9T6hofePtB8MAFZsJkGhiFyuwq2nKtZxazB1iUag8KP\"]}},\"version\":1}", "bytecode": "0x6080604052335f806101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555034801561004e575f80fd5b506103118061005c5f395ff3fe608060405234801561000f575f80fd5b506004361061003f575f3560e01c8063445df0ac146100435780638da5cb5b14610061578063fdacd5761461007f575b5f80fd5b61004b61009b565b6040516100589190610173565b60405180910390f35b6100696100a1565b60405161007691906101cb565b60405180910390f35b61009960048036038101906100949190610212565b6100c4565b005b60015481565b5f8054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b5f8054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610151576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610148906102bd565b60405180910390fd5b8060018190555050565b5f819050919050565b61016d8161015b565b82525050565b5f6020820190506101865f830184610164565b92915050565b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f6101b58261018c565b9050919050565b6101c5816101ab565b82525050565b5f6020820190506101de5f8301846101bc565b92915050565b5f80fd5b6101f18161015b565b81146101fb575f80fd5b50565b5f8135905061020c816101e8565b92915050565b5f60208284031215610227576102266101e4565b5b5f610234848285016101fe565b91505092915050565b5f82825260208201905092915050565b7f546869732066756e6374696f6e206973207265737472696374656420746f20745f8201527f686520636f6e74726163742773206f776e657200000000000000000000000000602082015250565b5f6102a760338361023d565b91506102b28261024d565b604082019050919050565b5f6020820190508181035f8301526102d48161029b565b905091905056fea2646970667358221220233027ecf6c5243babd4649ad3dfbb763f38a9f45c5ba87544ea91c9735c933864736f6c63430008150033", "deployedBytecode": "0x608060405234801561000f575f80fd5b506004361061003f575f3560e01c8063445df0ac146100435780638da5cb5b14610061578063fdacd5761461007f575b5f80fd5b61004b61009b565b6040516100589190610173565b60405180910390f35b6100696100a1565b60405161007691906101cb565b60405180910390f35b61009960048036038101906100949190610212565b6100c4565b005b60015481565b5f8054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b5f8054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610151576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610148906102bd565b60405180910390fd5b8060018190555050565b5f819050919050565b61016d8161015b565b82525050565b5f6020820190506101865f830184610164565b92915050565b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f6101b58261018c565b9050919050565b6101c5816101ab565b82525050565b5f6020820190506101de5f8301846101bc565b92915050565b5f80fd5b6101f18161015b565b81146101fb575f80fd5b50565b5f8135905061020c816101e8565b92915050565b5f60208284031215610227576102266101e4565b5b5f610234848285016101fe565b91505092915050565b5f82825260208201905092915050565b7f546869732066756e6374696f6e206973207265737472696374656420746f20745f8201527f686520636f6e74726163742773206f776e657200000000000000000000000000602082015250565b5f6102a760338361023d565b91506102b28261024d565b604082019050919050565b5f6020820190508181035f8301526102d48161029b565b905091905056fea2646970667358221220233027ecf6c5243babd4649ad3dfbb763f38a9f45c5ba87544ea91c9735c933864736f6c63430008150033", "immutableReferences": {}, "generatedSources": [], "deployedGeneratedSources": [{"ast": {"nativeSrc": "0:3176:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:3176:2", "statements": [{"body": {"nativeSrc": "52:32:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "52:32:2", "statements": [{"nativeSrc": "62:16:2", "nodeType": "YulAssignment", "src": "62:16:2", "value": {"name": "value", "nativeSrc": "73:5:2", "nodeType": "YulIdentifier", "src": "73:5:2"}, "variableNames": [{"name": "cleaned", "nativeSrc": "62:7:2", "nodeType": "YulIdentifier", "src": "62:7:2"}]}]}, "name": "cleanup_t_uint256", "nativeSrc": "7:77:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "34:5:2", "nodeType": "YulTypedName", "src": "34:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "44:7:2", "nodeType": "YulTypedName", "src": "44:7:2", "type": ""}], "src": "7:77:2"}, {"body": {"nativeSrc": "155:53:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "155:53:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "172:3:2", "nodeType": "YulIdentifier", "src": "172:3:2"}, {"arguments": [{"name": "value", "nativeSrc": "195:5:2", "nodeType": "YulIdentifier", "src": "195:5:2"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "177:17:2", "nodeType": "YulIdentifier", "src": "177:17:2"}, "nativeSrc": "177:24:2", "nodeType": "YulFunctionCall", "src": "177:24:2"}], "functionName": {"name": "mstore", "nativeSrc": "165:6:2", "nodeType": "YulIdentifier", "src": "165:6:2"}, "nativeSrc": "165:37:2", "nodeType": "YulFunctionCall", "src": "165:37:2"}, "nativeSrc": "165:37:2", "nodeType": "YulExpressionStatement", "src": "165:37:2"}]}, "name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nativeSrc": "90:118:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "143:5:2", "nodeType": "YulTypedName", "src": "143:5:2", "type": ""}, {"name": "pos", "nativeSrc": "150:3:2", "nodeType": "YulTypedName", "src": "150:3:2", "type": ""}], "src": "90:118:2"}, {"body": {"nativeSrc": "312:124:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "312:124:2", "statements": [{"nativeSrc": "322:26:2", "nodeType": "YulAssignment", "src": "322:26:2", "value": {"arguments": [{"name": "headStart", "nativeSrc": "334:9:2", "nodeType": "YulIdentifier", "src": "334:9:2"}, {"kind": "number", "nativeSrc": "345:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "345:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "330:3:2", "nodeType": "YulIdentifier", "src": "330:3:2"}, "nativeSrc": "330:18:2", "nodeType": "YulFunctionCall", "src": "330:18:2"}, "variableNames": [{"name": "tail", "nativeSrc": "322:4:2", "nodeType": "YulIdentifier", "src": "322:4:2"}]}, {"expression": {"arguments": [{"name": "value0", "nativeSrc": "402:6:2", "nodeType": "YulIdentifier", "src": "402:6:2"}, {"arguments": [{"name": "headStart", "nativeSrc": "415:9:2", "nodeType": "YulIdentifier", "src": "415:9:2"}, {"kind": "number", "nativeSrc": "426:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "426:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "411:3:2", "nodeType": "YulIdentifier", "src": "411:3:2"}, "nativeSrc": "411:17:2", "nodeType": "YulFunctionCall", "src": "411:17:2"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nativeSrc": "358:43:2", "nodeType": "YulIdentifier", "src": "358:43:2"}, "nativeSrc": "358:71:2", "nodeType": "YulFunctionCall", "src": "358:71:2"}, "nativeSrc": "358:71:2", "nodeType": "YulExpressionStatement", "src": "358:71:2"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nativeSrc": "214:222:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "284:9:2", "nodeType": "YulTypedName", "src": "284:9:2", "type": ""}, {"name": "value0", "nativeSrc": "296:6:2", "nodeType": "YulTypedName", "src": "296:6:2", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "307:4:2", "nodeType": "YulTypedName", "src": "307:4:2", "type": ""}], "src": "214:222:2"}, {"body": {"nativeSrc": "487:81:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "487:81:2", "statements": [{"nativeSrc": "497:65:2", "nodeType": "YulAssignment", "src": "497:65:2", "value": {"arguments": [{"name": "value", "nativeSrc": "512:5:2", "nodeType": "YulIdentifier", "src": "512:5:2"}, {"kind": "number", "nativeSrc": "519:42:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "519:42:2", "type": "", "value": "0xffffffffffffffffffffffffffffffffffffffff"}], "functionName": {"name": "and", "nativeSrc": "508:3:2", "nodeType": "YulIdentifier", "src": "508:3:2"}, "nativeSrc": "508:54:2", "nodeType": "YulFunctionCall", "src": "508:54:2"}, "variableNames": [{"name": "cleaned", "nativeSrc": "497:7:2", "nodeType": "YulIdentifier", "src": "497:7:2"}]}]}, "name": "cleanup_t_uint160", "nativeSrc": "442:126:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "469:5:2", "nodeType": "YulTypedName", "src": "469:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "479:7:2", "nodeType": "YulTypedName", "src": "479:7:2", "type": ""}], "src": "442:126:2"}, {"body": {"nativeSrc": "619:51:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "619:51:2", "statements": [{"nativeSrc": "629:35:2", "nodeType": "YulAssignment", "src": "629:35:2", "value": {"arguments": [{"name": "value", "nativeSrc": "658:5:2", "nodeType": "YulIdentifier", "src": "658:5:2"}], "functionName": {"name": "cleanup_t_uint160", "nativeSrc": "640:17:2", "nodeType": "YulIdentifier", "src": "640:17:2"}, "nativeSrc": "640:24:2", "nodeType": "YulFunctionCall", "src": "640:24:2"}, "variableNames": [{"name": "cleaned", "nativeSrc": "629:7:2", "nodeType": "YulIdentifier", "src": "629:7:2"}]}]}, "name": "cleanup_t_address", "nativeSrc": "574:96:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "601:5:2", "nodeType": "YulTypedName", "src": "601:5:2", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "611:7:2", "nodeType": "YulTypedName", "src": "611:7:2", "type": ""}], "src": "574:96:2"}, {"body": {"nativeSrc": "741:53:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "741:53:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "758:3:2", "nodeType": "YulIdentifier", "src": "758:3:2"}, {"arguments": [{"name": "value", "nativeSrc": "781:5:2", "nodeType": "YulIdentifier", "src": "781:5:2"}], "functionName": {"name": "cleanup_t_address", "nativeSrc": "763:17:2", "nodeType": "YulIdentifier", "src": "763:17:2"}, "nativeSrc": "763:24:2", "nodeType": "YulFunctionCall", "src": "763:24:2"}], "functionName": {"name": "mstore", "nativeSrc": "751:6:2", "nodeType": "YulIdentifier", "src": "751:6:2"}, "nativeSrc": "751:37:2", "nodeType": "YulFunctionCall", "src": "751:37:2"}, "nativeSrc": "751:37:2", "nodeType": "YulExpressionStatement", "src": "751:37:2"}]}, "name": "abi_encode_t_address_to_t_address_fromStack", "nativeSrc": "676:118:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "729:5:2", "nodeType": "YulTypedName", "src": "729:5:2", "type": ""}, {"name": "pos", "nativeSrc": "736:3:2", "nodeType": "YulTypedName", "src": "736:3:2", "type": ""}], "src": "676:118:2"}, {"body": {"nativeSrc": "898:124:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "898:124:2", "statements": [{"nativeSrc": "908:26:2", "nodeType": "YulAssignment", "src": "908:26:2", "value": {"arguments": [{"name": "headStart", "nativeSrc": "920:9:2", "nodeType": "YulIdentifier", "src": "920:9:2"}, {"kind": "number", "nativeSrc": "931:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "931:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "916:3:2", "nodeType": "YulIdentifier", "src": "916:3:2"}, "nativeSrc": "916:18:2", "nodeType": "YulFunctionCall", "src": "916:18:2"}, "variableNames": [{"name": "tail", "nativeSrc": "908:4:2", "nodeType": "YulIdentifier", "src": "908:4:2"}]}, {"expression": {"arguments": [{"name": "value0", "nativeSrc": "988:6:2", "nodeType": "YulIdentifier", "src": "988:6:2"}, {"arguments": [{"name": "headStart", "nativeSrc": "1001:9:2", "nodeType": "YulIdentifier", "src": "1001:9:2"}, {"kind": "number", "nativeSrc": "1012:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1012:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "997:3:2", "nodeType": "YulIdentifier", "src": "997:3:2"}, "nativeSrc": "997:17:2", "nodeType": "YulFunctionCall", "src": "997:17:2"}], "functionName": {"name": "abi_encode_t_address_to_t_address_fromStack", "nativeSrc": "944:43:2", "nodeType": "YulIdentifier", "src": "944:43:2"}, "nativeSrc": "944:71:2", "nodeType": "YulFunctionCall", "src": "944:71:2"}, "nativeSrc": "944:71:2", "nodeType": "YulExpressionStatement", "src": "944:71:2"}]}, "name": "abi_encode_tuple_t_address__to_t_address__fromStack_reversed", "nativeSrc": "800:222:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "870:9:2", "nodeType": "YulTypedName", "src": "870:9:2", "type": ""}, {"name": "value0", "nativeSrc": "882:6:2", "nodeType": "YulTypedName", "src": "882:6:2", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "893:4:2", "nodeType": "YulTypedName", "src": "893:4:2", "type": ""}], "src": "800:222:2"}, {"body": {"nativeSrc": "1068:35:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1068:35:2", "statements": [{"nativeSrc": "1078:19:2", "nodeType": "YulAssignment", "src": "1078:19:2", "value": {"arguments": [{"kind": "number", "nativeSrc": "1094:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1094:2:2", "type": "", "value": "64"}], "functionName": {"name": "mload", "nativeSrc": "1088:5:2", "nodeType": "YulIdentifier", "src": "1088:5:2"}, "nativeSrc": "1088:9:2", "nodeType": "YulFunctionCall", "src": "1088:9:2"}, "variableNames": [{"name": "memPtr", "nativeSrc": "1078:6:2", "nodeType": "YulIdentifier", "src": "1078:6:2"}]}]}, "name": "allocate_unbounded", "nativeSrc": "1028:75:2", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "memPtr", "nativeSrc": "1061:6:2", "nodeType": "YulTypedName", "src": "1061:6:2", "type": ""}], "src": "1028:75:2"}, {"body": {"nativeSrc": "1198:28:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1198:28:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "1215:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1215:1:2", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "1218:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1218:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "1208:6:2", "nodeType": "YulIdentifier", "src": "1208:6:2"}, "nativeSrc": "1208:12:2", "nodeType": "YulFunctionCall", "src": "1208:12:2"}, "nativeSrc": "1208:12:2", "nodeType": "YulExpressionStatement", "src": "1208:12:2"}]}, "name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "1109:117:2", "nodeType": "YulFunctionDefinition", "src": "1109:117:2"}, {"body": {"nativeSrc": "1321:28:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1321:28:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "1338:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1338:1:2", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "1341:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1341:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "1331:6:2", "nodeType": "YulIdentifier", "src": "1331:6:2"}, "nativeSrc": "1331:12:2", "nodeType": "YulFunctionCall", "src": "1331:12:2"}, "nativeSrc": "1331:12:2", "nodeType": "YulExpressionStatement", "src": "1331:12:2"}]}, "name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nativeSrc": "1232:117:2", "nodeType": "YulFunctionDefinition", "src": "1232:117:2"}, {"body": {"nativeSrc": "1398:79:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1398:79:2", "statements": [{"body": {"nativeSrc": "1455:16:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1455:16:2", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "1464:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1464:1:2", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "1467:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1467:1:2", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "1457:6:2", "nodeType": "YulIdentifier", "src": "1457:6:2"}, "nativeSrc": "1457:12:2", "nodeType": "YulFunctionCall", "src": "1457:12:2"}, "nativeSrc": "1457:12:2", "nodeType": "YulExpressionStatement", "src": "1457:12:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "1421:5:2", "nodeType": "YulIdentifier", "src": "1421:5:2"}, {"arguments": [{"name": "value", "nativeSrc": "1446:5:2", "nodeType": "YulIdentifier", "src": "1446:5:2"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "1428:17:2", "nodeType": "YulIdentifier", "src": "1428:17:2"}, "nativeSrc": "1428:24:2", "nodeType": "YulFunctionCall", "src": "1428:24:2"}], "functionName": {"name": "eq", "nativeSrc": "1418:2:2", "nodeType": "YulIdentifier", "src": "1418:2:2"}, "nativeSrc": "1418:35:2", "nodeType": "YulFunctionCall", "src": "1418:35:2"}], "functionName": {"name": "iszero", "nativeSrc": "1411:6:2", "nodeType": "YulIdentifier", "src": "1411:6:2"}, "nativeSrc": "1411:43:2", "nodeType": "YulFunctionCall", "src": "1411:43:2"}, "nativeSrc": "1408:63:2", "nodeType": "YulIf", "src": "1408:63:2"}]}, "name": "validator_revert_t_uint256", "nativeSrc": "1355:122:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1391:5:2", "nodeType": "YulTypedName", "src": "1391:5:2", "type": ""}], "src": "1355:122:2"}, {"body": {"nativeSrc": "1535:87:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1535:87:2", "statements": [{"nativeSrc": "1545:29:2", "nodeType": "YulAssignment", "src": "1545:29:2", "value": {"arguments": [{"name": "offset", "nativeSrc": "1567:6:2", "nodeType": "YulIdentifier", "src": "1567:6:2"}], "functionName": {"name": "calldataload", "nativeSrc": "1554:12:2", "nodeType": "YulIdentifier", "src": "1554:12:2"}, "nativeSrc": "1554:20:2", "nodeType": "YulFunctionCall", "src": "1554:20:2"}, "variableNames": [{"name": "value", "nativeSrc": "1545:5:2", "nodeType": "YulIdentifier", "src": "1545:5:2"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "1610:5:2", "nodeType": "YulIdentifier", "src": "1610:5:2"}], "functionName": {"name": "validator_revert_t_uint256", "nativeSrc": "1583:26:2", "nodeType": "YulIdentifier", "src": "1583:26:2"}, "nativeSrc": "1583:33:2", "nodeType": "YulFunctionCall", "src": "1583:33:2"}, "nativeSrc": "1583:33:2", "nodeType": "YulExpressionStatement", "src": "1583:33:2"}]}, "name": "abi_decode_t_uint256", "nativeSrc": "1483:139:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "1513:6:2", "nodeType": "YulTypedName", "src": "1513:6:2", "type": ""}, {"name": "end", "nativeSrc": "1521:3:2", "nodeType": "YulTypedName", "src": "1521:3:2", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "1529:5:2", "nodeType": "YulTypedName", "src": "1529:5:2", "type": ""}], "src": "1483:139:2"}, {"body": {"nativeSrc": "1694:263:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1694:263:2", "statements": [{"body": {"nativeSrc": "1740:83:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1740:83:2", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "1742:77:2", "nodeType": "YulIdentifier", "src": "1742:77:2"}, "nativeSrc": "1742:79:2", "nodeType": "YulFunctionCall", "src": "1742:79:2"}, "nativeSrc": "1742:79:2", "nodeType": "YulExpressionStatement", "src": "1742:79:2"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "1715:7:2", "nodeType": "YulIdentifier", "src": "1715:7:2"}, {"name": "headStart", "nativeSrc": "1724:9:2", "nodeType": "YulIdentifier", "src": "1724:9:2"}], "functionName": {"name": "sub", "nativeSrc": "1711:3:2", "nodeType": "YulIdentifier", "src": "1711:3:2"}, "nativeSrc": "1711:23:2", "nodeType": "YulFunctionCall", "src": "1711:23:2"}, {"kind": "number", "nativeSrc": "1736:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1736:2:2", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "1707:3:2", "nodeType": "YulIdentifier", "src": "1707:3:2"}, "nativeSrc": "1707:32:2", "nodeType": "YulFunctionCall", "src": "1707:32:2"}, "nativeSrc": "1704:119:2", "nodeType": "YulIf", "src": "1704:119:2"}, {"nativeSrc": "1833:117:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1833:117:2", "statements": [{"nativeSrc": "1848:15:2", "nodeType": "YulVariableDeclaration", "src": "1848:15:2", "value": {"kind": "number", "nativeSrc": "1862:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1862:1:2", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "1852:6:2", "nodeType": "YulTypedName", "src": "1852:6:2", "type": ""}]}, {"nativeSrc": "1877:63:2", "nodeType": "YulAssignment", "src": "1877:63:2", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "1912:9:2", "nodeType": "YulIdentifier", "src": "1912:9:2"}, {"name": "offset", "nativeSrc": "1923:6:2", "nodeType": "YulIdentifier", "src": "1923:6:2"}], "functionName": {"name": "add", "nativeSrc": "1908:3:2", "nodeType": "YulIdentifier", "src": "1908:3:2"}, "nativeSrc": "1908:22:2", "nodeType": "YulFunctionCall", "src": "1908:22:2"}, {"name": "dataEnd", "nativeSrc": "1932:7:2", "nodeType": "YulIdentifier", "src": "1932:7:2"}], "functionName": {"name": "abi_decode_t_uint256", "nativeSrc": "1887:20:2", "nodeType": "YulIdentifier", "src": "1887:20:2"}, "nativeSrc": "1887:53:2", "nodeType": "YulFunctionCall", "src": "1887:53:2"}, "variableNames": [{"name": "value0", "nativeSrc": "1877:6:2", "nodeType": "YulIdentifier", "src": "1877:6:2"}]}]}]}, "name": "abi_decode_tuple_t_uint256", "nativeSrc": "1628:329:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "1664:9:2", "nodeType": "YulTypedName", "src": "1664:9:2", "type": ""}, {"name": "dataEnd", "nativeSrc": "1675:7:2", "nodeType": "YulTypedName", "src": "1675:7:2", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "1687:6:2", "nodeType": "YulTypedName", "src": "1687:6:2", "type": ""}], "src": "1628:329:2"}, {"body": {"nativeSrc": "2059:73:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2059:73:2", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "2076:3:2", "nodeType": "YulIdentifier", "src": "2076:3:2"}, {"name": "length", "nativeSrc": "2081:6:2", "nodeType": "YulIdentifier", "src": "2081:6:2"}], "functionName": {"name": "mstore", "nativeSrc": "2069:6:2", "nodeType": "YulIdentifier", "src": "2069:6:2"}, "nativeSrc": "2069:19:2", "nodeType": "YulFunctionCall", "src": "2069:19:2"}, "nativeSrc": "2069:19:2", "nodeType": "YulExpressionStatement", "src": "2069:19:2"}, {"nativeSrc": "2097:29:2", "nodeType": "YulAssignment", "src": "2097:29:2", "value": {"arguments": [{"name": "pos", "nativeSrc": "2116:3:2", "nodeType": "YulIdentifier", "src": "2116:3:2"}, {"kind": "number", "nativeSrc": "2121:4:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2121:4:2", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2112:3:2", "nodeType": "YulIdentifier", "src": "2112:3:2"}, "nativeSrc": "2112:14:2", "nodeType": "YulFunctionCall", "src": "2112:14:2"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "2097:11:2", "nodeType": "YulIdentifier", "src": "2097:11:2"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nativeSrc": "1963:169:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "2031:3:2", "nodeType": "YulTypedName", "src": "2031:3:2", "type": ""}, {"name": "length", "nativeSrc": "2036:6:2", "nodeType": "YulTypedName", "src": "2036:6:2", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "2047:11:2", "nodeType": "YulTypedName", "src": "2047:11:2", "type": ""}], "src": "1963:169:2"}, {"body": {"nativeSrc": "2244:132:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2244:132:2", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "memPtr", "nativeSrc": "2266:6:2", "nodeType": "YulIdentifier", "src": "2266:6:2"}, {"kind": "number", "nativeSrc": "2274:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2274:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "2262:3:2", "nodeType": "YulIdentifier", "src": "2262:3:2"}, "nativeSrc": "2262:14:2", "nodeType": "YulFunctionCall", "src": "2262:14:2"}, {"hexValue": "546869732066756e6374696f6e206973207265737472696374656420746f2074", "kind": "string", "nativeSrc": "2278:34:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2278:34:2", "type": "", "value": "This function is restricted to t"}], "functionName": {"name": "mstore", "nativeSrc": "2255:6:2", "nodeType": "YulIdentifier", "src": "2255:6:2"}, "nativeSrc": "2255:58:2", "nodeType": "YulFunctionCall", "src": "2255:58:2"}, "nativeSrc": "2255:58:2", "nodeType": "YulExpressionStatement", "src": "2255:58:2"}, {"expression": {"arguments": [{"arguments": [{"name": "memPtr", "nativeSrc": "2334:6:2", "nodeType": "YulIdentifier", "src": "2334:6:2"}, {"kind": "number", "nativeSrc": "2342:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2342:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2330:3:2", "nodeType": "YulIdentifier", "src": "2330:3:2"}, "nativeSrc": "2330:15:2", "nodeType": "YulFunctionCall", "src": "2330:15:2"}, {"hexValue": "686520636f6e74726163742773206f776e6572", "kind": "string", "nativeSrc": "2347:21:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2347:21:2", "type": "", "value": "he contract's owner"}], "functionName": {"name": "mstore", "nativeSrc": "2323:6:2", "nodeType": "YulIdentifier", "src": "2323:6:2"}, "nativeSrc": "2323:46:2", "nodeType": "YulFunctionCall", "src": "2323:46:2"}, "nativeSrc": "2323:46:2", "nodeType": "YulExpressionStatement", "src": "2323:46:2"}]}, "name": "store_literal_in_memory_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1", "nativeSrc": "2138:238:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "memPtr", "nativeSrc": "2236:6:2", "nodeType": "YulTypedName", "src": "2236:6:2", "type": ""}], "src": "2138:238:2"}, {"body": {"nativeSrc": "2528:220:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2528:220:2", "statements": [{"nativeSrc": "2538:74:2", "nodeType": "YulAssignment", "src": "2538:74:2", "value": {"arguments": [{"name": "pos", "nativeSrc": "2604:3:2", "nodeType": "YulIdentifier", "src": "2604:3:2"}, {"kind": "number", "nativeSrc": "2609:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2609:2:2", "type": "", "value": "51"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nativeSrc": "2545:58:2", "nodeType": "YulIdentifier", "src": "2545:58:2"}, "nativeSrc": "2545:67:2", "nodeType": "YulFunctionCall", "src": "2545:67:2"}, "variableNames": [{"name": "pos", "nativeSrc": "2538:3:2", "nodeType": "YulIdentifier", "src": "2538:3:2"}]}, {"expression": {"arguments": [{"name": "pos", "nativeSrc": "2710:3:2", "nodeType": "YulIdentifier", "src": "2710:3:2"}], "functionName": {"name": "store_literal_in_memory_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1", "nativeSrc": "2621:88:2", "nodeType": "YulIdentifier", "src": "2621:88:2"}, "nativeSrc": "2621:93:2", "nodeType": "YulFunctionCall", "src": "2621:93:2"}, "nativeSrc": "2621:93:2", "nodeType": "YulExpressionStatement", "src": "2621:93:2"}, {"nativeSrc": "2723:19:2", "nodeType": "YulAssignment", "src": "2723:19:2", "value": {"arguments": [{"name": "pos", "nativeSrc": "2734:3:2", "nodeType": "YulIdentifier", "src": "2734:3:2"}, {"kind": "number", "nativeSrc": "2739:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2739:2:2", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "2730:3:2", "nodeType": "YulIdentifier", "src": "2730:3:2"}, "nativeSrc": "2730:12:2", "nodeType": "YulFunctionCall", "src": "2730:12:2"}, "variableNames": [{"name": "end", "nativeSrc": "2723:3:2", "nodeType": "YulIdentifier", "src": "2723:3:2"}]}]}, "name": "abi_encode_t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1_to_t_string_memory_ptr_fromStack", "nativeSrc": "2382:366:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "2516:3:2", "nodeType": "YulTypedName", "src": "2516:3:2", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "2524:3:2", "nodeType": "YulTypedName", "src": "2524:3:2", "type": ""}], "src": "2382:366:2"}, {"body": {"nativeSrc": "2925:248:2", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2925:248:2", "statements": [{"nativeSrc": "2935:26:2", "nodeType": "YulAssignment", "src": "2935:26:2", "value": {"arguments": [{"name": "headStart", "nativeSrc": "2947:9:2", "nodeType": "YulIdentifier", "src": "2947:9:2"}, {"kind": "number", "nativeSrc": "2958:2:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2958:2:2", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "2943:3:2", "nodeType": "YulIdentifier", "src": "2943:3:2"}, "nativeSrc": "2943:18:2", "nodeType": "YulFunctionCall", "src": "2943:18:2"}, "variableNames": [{"name": "tail", "nativeSrc": "2935:4:2", "nodeType": "YulIdentifier", "src": "2935:4:2"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "2982:9:2", "nodeType": "YulIdentifier", "src": "2982:9:2"}, {"kind": "number", "nativeSrc": "2993:1:2", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2993:1:2", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "2978:3:2", "nodeType": "YulIdentifier", "src": "2978:3:2"}, "nativeSrc": "2978:17:2", "nodeType": "YulFunctionCall", "src": "2978:17:2"}, {"arguments": [{"name": "tail", "nativeSrc": "3001:4:2", "nodeType": "YulIdentifier", "src": "3001:4:2"}, {"name": "headStart", "nativeSrc": "3007:9:2", "nodeType": "YulIdentifier", "src": "3007:9:2"}], "functionName": {"name": "sub", "nativeSrc": "2997:3:2", "nodeType": "YulIdentifier", "src": "2997:3:2"}, "nativeSrc": "2997:20:2", "nodeType": "YulFunctionCall", "src": "2997:20:2"}], "functionName": {"name": "mstore", "nativeSrc": "2971:6:2", "nodeType": "YulIdentifier", "src": "2971:6:2"}, "nativeSrc": "2971:47:2", "nodeType": "YulFunctionCall", "src": "2971:47:2"}, "nativeSrc": "2971:47:2", "nodeType": "YulExpressionStatement", "src": "2971:47:2"}, {"nativeSrc": "3027:139:2", "nodeType": "YulAssignment", "src": "3027:139:2", "value": {"arguments": [{"name": "tail", "nativeSrc": "3161:4:2", "nodeType": "YulIdentifier", "src": "3161:4:2"}], "functionName": {"name": "abi_encode_t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1_to_t_string_memory_ptr_fromStack", "nativeSrc": "3035:124:2", "nodeType": "YulIdentifier", "src": "3035:124:2"}, "nativeSrc": "3035:131:2", "nodeType": "YulFunctionCall", "src": "3035:131:2"}, "variableNames": [{"name": "tail", "nativeSrc": "3027:4:2", "nodeType": "YulIdentifier", "src": "3027:4:2"}]}]}, "name": "abi_encode_tuple_t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1__to_t_string_memory_ptr__fromStack_reversed", "nativeSrc": "2754:419:2", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "2905:9:2", "nodeType": "YulTypedName", "src": "2905:9:2", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "2920:4:2", "nodeType": "YulTypedName", "src": "2920:4:2", "type": ""}], "src": "2754:419:2"}]}, "contents": "{\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function abi_encode_t_uint256_to_t_uint256_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function cleanup_t_uint160(value) -> cleaned {\n        cleaned := and(value, 0xffffffffffffffffffffffffffffffffffffffff)\n    }\n\n    function cleanup_t_address(value) -> cleaned {\n        cleaned := cleanup_t_uint160(value)\n    }\n\n    function abi_encode_t_address_to_t_address_fromStack(value, pos) {\n        mstore(pos, cleanup_t_address(value))\n    }\n\n    function abi_encode_tuple_t_address__to_t_address__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_address_to_t_address_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function allocate_unbounded() -> memPtr {\n        memPtr := mload(64)\n    }\n\n    function revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() {\n        revert(0, 0)\n    }\n\n    function revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() {\n        revert(0, 0)\n    }\n\n    function validator_revert_t_uint256(value) {\n        if iszero(eq(value, cleanup_t_uint256(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint256(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint256(value)\n    }\n\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function store_literal_in_memory_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1(memPtr) {\n\n        mstore(add(memPtr, 0), \"This function is restricted to t\")\n\n        mstore(add(memPtr, 32), \"he contract's owner\")\n\n    }\n\n    function abi_encode_t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1_to_t_string_memory_ptr_fromStack(pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, 51)\n        store_literal_in_memory_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1(pos)\n        end := add(pos, 64)\n    }\n\n    function abi_encode_tuple_t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1__to_t_string_memory_ptr__fromStack_reversed(headStart ) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1_to_t_string_memory_ptr_fromStack( tail)\n\n    }\n\n}\n", "id": 2, "language": "<PERSON>l", "name": "#utility.yul"}], "sourceMap": "57:352:0:-:0;;;104:10;81:33;;;;;;;;;;;;;;;;;;;;57:352;;;;;;;;;;;;;;;;", "deployedSourceMap": "57:352:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;118:36;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;81:33;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;304:103;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;118:36;;;;:::o;81:33::-;;;;;;;;;;;;:::o;304:103::-;216:5;;;;;;;;;;202:19;;:10;:19;;;187:101;;;;;;;;;;;;:::i;:::-;;;;;;;;;393:9:::1;366:24;:36;;;;304:103:::0;:::o;7:77:2:-;44:7;73:5;62:16;;7:77;;;:::o;90:118::-;177:24;195:5;177:24;:::i;:::-;172:3;165:37;90:118;;:::o;214:222::-;307:4;345:2;334:9;330:18;322:26;;358:71;426:1;415:9;411:17;402:6;358:71;:::i;:::-;214:222;;;;:::o;442:126::-;479:7;519:42;512:5;508:54;497:65;;442:126;;;:::o;574:96::-;611:7;640:24;658:5;640:24;:::i;:::-;629:35;;574:96;;;:::o;676:118::-;763:24;781:5;763:24;:::i;:::-;758:3;751:37;676:118;;:::o;800:222::-;893:4;931:2;920:9;916:18;908:26;;944:71;1012:1;1001:9;997:17;988:6;944:71;:::i;:::-;800:222;;;;:::o;1109:117::-;1218:1;1215;1208:12;1355:122;1428:24;1446:5;1428:24;:::i;:::-;1421:5;1418:35;1408:63;;1467:1;1464;1457:12;1408:63;1355:122;:::o;1483:139::-;1529:5;1567:6;1554:20;1545:29;;1583:33;1610:5;1583:33;:::i;:::-;1483:139;;;;:::o;1628:329::-;1687:6;1736:2;1724:9;1715:7;1711:23;1707:32;1704:119;;;1742:79;;:::i;:::-;1704:119;1862:1;1887:53;1932:7;1923:6;1912:9;1908:22;1887:53;:::i;:::-;1877:63;;1833:117;1628:329;;;;:::o;1963:169::-;2047:11;2081:6;2076:3;2069:19;2121:4;2116:3;2112:14;2097:29;;1963:169;;;;:::o;2138:238::-;2278:34;2274:1;2266:6;2262:14;2255:58;2347:21;2342:2;2334:6;2330:15;2323:46;2138:238;:::o;2382:366::-;2524:3;2545:67;2609:2;2604:3;2545:67;:::i;:::-;2538:74;;2621:93;2710:3;2621:93;:::i;:::-;2739:2;2734:3;2730:12;2723:19;;2382:366;;;:::o;2754:419::-;2920:4;2958:2;2947:9;2943:18;2935:26;;3007:9;3001:4;2997:20;2993:1;2982:9;2978:17;2971:47;3035:131;3161:4;3035:131;:::i;:::-;3027:139;;2754:419;;;:::o", "source": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.0;\n\ncontract Migrations {\n  address public owner = msg.sender;\n  uint public last_completed_migration;\n\n  modifier restricted() {\n    require(\n      msg.sender == owner,\n      \"This function is restricted to the contract's owner\"\n    );\n    _;\n  }\n\n  function setCompleted(uint completed) public restricted {\n    last_completed_migration = completed;\n  }\n}\n", "sourcePath": "C:\\Users\\<USER>\\Downloads\\SongRegistry-SmartContract\\thirdProject\\contracts\\Migrations.sol", "ast": {"absolutePath": "project:/contracts/Migrations.sol", "exportedSymbols": {"Migrations": [32]}, "id": 33, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".0"], "nodeType": "PragmaDirective", "src": "32:23:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "Migrations", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 32, "linearizedBaseContracts": [32], "name": "Migrations", "nameLocation": "66:10:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "functionSelector": "8da5cb5b", "id": 5, "mutability": "mutable", "name": "owner", "nameLocation": "96:5:0", "nodeType": "VariableDeclaration", "scope": 32, "src": "81:33:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 2, "name": "address", "nodeType": "ElementaryTypeName", "src": "81:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"expression": {"id": 3, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967281, "src": "104:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 4, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "108:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "104:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "public"}, {"constant": false, "functionSelector": "445df0ac", "id": 7, "mutability": "mutable", "name": "last_completed_migration", "nameLocation": "130:24:0", "nodeType": "VariableDeclaration", "scope": 32, "src": "118:36:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 6, "name": "uint", "nodeType": "ElementaryTypeName", "src": "118:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "public"}, {"body": {"id": 18, "nodeType": "Block", "src": "181:119:0", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 13, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 10, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967281, "src": "202:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 11, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "206:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "202:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 12, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 5, "src": "216:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "202:19:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "546869732066756e6374696f6e206973207265737472696374656420746f2074686520636f6e74726163742773206f776e6572", "id": 14, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "229:53:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1", "typeString": "literal_string \"This function is restricted to the contract's owner\""}, "value": "This function is restricted to the contract's owner"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_f60fe2d9d123295bf92ecf95167f1fa709e374da35e4c083bd39dc2d82acd8b1", "typeString": "literal_string \"This function is restricted to the contract's owner\""}], "id": 9, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4294967278, 4294967278], "referencedDeclaration": 4294967278, "src": "187:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 15, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "187:101:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 16, "nodeType": "ExpressionStatement", "src": "187:101:0"}, {"id": 17, "nodeType": "PlaceholderStatement", "src": "294:1:0"}]}, "id": 19, "name": "restricted", "nameLocation": "168:10:0", "nodeType": "ModifierDefinition", "parameters": {"id": 8, "nodeType": "ParameterList", "parameters": [], "src": "178:2:0"}, "src": "159:141:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 30, "nodeType": "Block", "src": "360:47:0", "statements": [{"expression": {"id": 28, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 26, "name": "last_completed_migration", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 7, "src": "366:24:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 27, "name": "completed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "393:9:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "366:36:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 29, "nodeType": "ExpressionStatement", "src": "366:36:0"}]}, "functionSelector": "fdacd576", "id": 31, "implemented": true, "kind": "function", "modifiers": [{"id": 24, "kind": "modifierInvocation", "modifierName": {"id": 23, "name": "restricted", "nameLocations": ["349:10:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 19, "src": "349:10:0"}, "nodeType": "ModifierInvocation", "src": "349:10:0"}], "name": "setCompleted", "nameLocation": "313:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 22, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 21, "mutability": "mutable", "name": "completed", "nameLocation": "331:9:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "326:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 20, "name": "uint", "nodeType": "ElementaryTypeName", "src": "326:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "325:16:0"}, "returnParameters": {"id": 25, "nodeType": "ParameterList", "parameters": [], "src": "360:0:0"}, "scope": 32, "src": "304:103:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}], "scope": 33, "src": "57:352:0", "usedErrors": [], "usedEvents": []}], "src": "32:378:0"}, "compiler": {"name": "solc", "version": "0.8.21+commit.d9974bed.Emscripten.clang"}, "networks": {}, "schemaVersion": "3.4.16", "updatedAt": "2025-06-17T07:10:35.427Z", "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}