{"contractName": "SongRegistry", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "songs", "outputs": [{"internalType": "string", "name": "title", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "string", "name": "url", "type": "string"}, {"internalType": "uint256", "name": "price", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}, {"inputs": [{"internalType": "string", "name": "_title", "type": "string"}, {"internalType": "string", "name": "_url", "type": "string"}, {"internalType": "uint256", "name": "_price", "type": "uint256"}], "name": "registerSong", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getNumberOfSongs", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}], "metadata": "{\"compiler\":{\"version\":\"0.8.19+commit.7dd6d404\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"getNumberOfSongs\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_title\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_url\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"_price\",\"type\":\"uint256\"}],\"name\":\"registerSong\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"songs\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"title\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"url\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"project:/contracts/songRegistry.sol\":\"SongRegistry\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/songRegistry.sol\":{\"keccak256\":\"0xb44b308d05a3bec36355485add7cd49eea6b42f321db1dc9f18125fab2d830c0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9556ac018c64e0428d44c724c7f09944e027b42552cebd1f9f332e9cd4aeed6d\",\"dweb:/ipfs/QmdDXvkQ84XKu1Riu1iApTLvdqvRJXbcXvF6Mythu3F7Nj\"]}},\"version\":1}", "bytecode": "0x608060405234801561001057600080fd5b506105db806100206000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c8063304cff301461004657806330753c7a14610072578063a8ec42d814610087575b600080fd5b6100596100543660046102a7565b610098565b6040516100699493929190610306565b60405180910390f35b6100856100803660046103ef565b6101f7565b005b600054604051908152602001610069565b600081815481106100a857600080fd5b90600052602060002090600402016000915090508060000180546100cb9061045c565b80601f01602080910402602001604051908101604052809291908181526020018280546100f79061045c565b80156101445780601f1061011957610100808354040283529160200191610144565b820191906000526020600020905b81548152906001019060200180831161012757829003601f168201915b505050600184015460028501805494956001600160a01b0390921694919350915061016e9061045c565b80601f016020809104026020016040519081016040528092919081815260200182805461019a9061045c565b80156101e75780601f106101bc576101008083540402835291602001916101e7565b820191906000526020600020905b8154815290600101906020018083116101ca57829003601f168201915b5050505050908060030154905084565b604080516080810182528481523360208201529081018390526060810182905260008054600181018255908052815160049091027f290decd9548b62a8d60345a988386fc84ba6bc95484008f6362f93160ef3e5630190819061025a90826104e5565b5060208201516001820180546001600160a01b0319166001600160a01b039092169190911790556040820151600282019061029590826104e5565b50606082015181600301555050505050565b6000602082840312156102b957600080fd5b5035919050565b6000815180845260005b818110156102e6576020818501810151868301820152016102ca565b506000602082860101526020601f19601f83011685010191505092915050565b60808152600061031960808301876102c0565b6001600160a01b0386166020840152828103604084015261033a81866102c0565b91505082606083015295945050505050565b634e487b7160e01b600052604160045260246000fd5b600082601f83011261037357600080fd5b813567ffffffffffffffff8082111561038e5761038e61034c565b604051601f8301601f19908116603f011681019082821181831017156103b6576103b661034c565b816040528381528660208588010111156103cf57600080fd5b836020870160208301376000602085830101528094505050505092915050565b60008060006060848603121561040457600080fd5b833567ffffffffffffffff8082111561041c57600080fd5b61042887838801610362565b9450602086013591508082111561043e57600080fd5b5061044b86828701610362565b925050604084013590509250925092565b600181811c9082168061047057607f821691505b60208210810361049057634e487b7160e01b600052602260045260246000fd5b50919050565b601f8211156104e057600081815260208120601f850160051c810160208610156104bd5750805b601f850160051c820191505b818110156104dc578281556001016104c9565b5050505b505050565b815167ffffffffffffffff8111156104ff576104ff61034c565b6105138161050d845461045c565b84610496565b602080601f83116001811461054857600084156105305750858301515b600019600386901b1c1916600185901b1785556104dc565b600085815260208120601f198616915b8281101561057757888601518255948401946001909101908401610558565b50858210156105955787850151600019600388901b60f8161c191681555b5050505050600190811b0190555056fea26469706673582212204df86bc675c21a04c8625ce4ac9e4b0638324667d39089ff7d1d60a60b980fe864736f6c63430008130033", "deployedBytecode": "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", "immutableReferences": {}, "generatedSources": [], "deployedGeneratedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:5425:1", "statements": [{"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6:3:1", "statements": []}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "84:110:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "130:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "139:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "142:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "132:6:1"}, "nodeType": "YulFunctionCall", "src": "132:12:1"}, "nodeType": "YulExpressionStatement", "src": "132:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "105:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "114:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "101:3:1"}, "nodeType": "YulFunctionCall", "src": "101:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "126:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "97:3:1"}, "nodeType": "YulFunctionCall", "src": "97:32:1"}, "nodeType": "YulIf", "src": "94:52:1"}, {"nodeType": "YulAssignment", "src": "155:33:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "178:9:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "165:12:1"}, "nodeType": "YulFunctionCall", "src": "165:23:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "155:6:1"}]}]}, "name": "abi_decode_tuple_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "50:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "61:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "73:6:1", "type": ""}], "src": "14:180:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "249:373:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "259:26:1", "value": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "279:5:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "273:5:1"}, "nodeType": "YulFunctionCall", "src": "273:12:1"}, "variables": [{"name": "length", "nodeType": "YulTypedName", "src": "263:6:1", "type": ""}]}, {"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "301:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "306:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "294:6:1"}, "nodeType": "YulFunctionCall", "src": "294:19:1"}, "nodeType": "YulExpressionStatement", "src": "294:19:1"}, {"nodeType": "YulVariableDeclaration", "src": "322:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "331:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "326:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "393:110:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "407:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "417:4:1", "type": "", "value": "0x20"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "411:2:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "449:3:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "454:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "445:3:1"}, "nodeType": "YulFunctionCall", "src": "445:11:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "458:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "441:3:1"}, "nodeType": "YulFunctionCall", "src": "441:20:1"}, {"arguments": [{"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "477:5:1"}, {"name": "i", "nodeType": "YulIdentifier", "src": "484:1:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "473:3:1"}, "nodeType": "YulFunctionCall", "src": "473:13:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "488:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "469:3:1"}, "nodeType": "YulFunctionCall", "src": "469:22:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "463:5:1"}, "nodeType": "YulFunctionCall", "src": "463:29:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "434:6:1"}, "nodeType": "YulFunctionCall", "src": "434:59:1"}, "nodeType": "YulExpressionStatement", "src": "434:59:1"}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "352:1:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "355:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "349:2:1"}, "nodeType": "YulFunctionCall", "src": "349:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "363:21:1", "statements": [{"nodeType": "YulAssignment", "src": "365:17:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "374:1:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "377:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "370:3:1"}, "nodeType": "YulFunctionCall", "src": "370:12:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "365:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "345:3:1", "statements": []}, "src": "341:162:1"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "527:3:1"}, {"name": "length", "nodeType": "YulIdentifier", "src": "532:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "523:3:1"}, "nodeType": "YulFunctionCall", "src": "523:16:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "541:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "519:3:1"}, "nodeType": "YulFunctionCall", "src": "519:27:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "548:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "512:6:1"}, "nodeType": "YulFunctionCall", "src": "512:38:1"}, "nodeType": "YulExpressionStatement", "src": "512:38:1"}, {"nodeType": "YulAssignment", "src": "559:57:1", "value": {"arguments": [{"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "574:3:1"}, {"arguments": [{"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "587:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "595:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "583:3:1"}, "nodeType": "YulFunctionCall", "src": "583:15:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "604:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "600:3:1"}, "nodeType": "YulFunctionCall", "src": "600:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "579:3:1"}, "nodeType": "YulFunctionCall", "src": "579:29:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "570:3:1"}, "nodeType": "YulFunctionCall", "src": "570:39:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "611:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "566:3:1"}, "nodeType": "YulFunctionCall", "src": "566:50:1"}, "variableNames": [{"name": "end", "nodeType": "YulIdentifier", "src": "559:3:1"}]}]}, "name": "abi_encode_string", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "226:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "233:3:1", "type": ""}], "returnVariables": [{"name": "end", "nodeType": "YulTypedName", "src": "241:3:1", "type": ""}], "src": "199:423:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "852:328:1", "statements": [{"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "869:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "880:3:1", "type": "", "value": "128"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "862:6:1"}, "nodeType": "YulFunctionCall", "src": "862:22:1"}, "nodeType": "YulExpressionStatement", "src": "862:22:1"}, {"nodeType": "YulVariableDeclaration", "src": "893:60:1", "value": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "925:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "937:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "948:3:1", "type": "", "value": "128"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "933:3:1"}, "nodeType": "YulFunctionCall", "src": "933:19:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "907:17:1"}, "nodeType": "YulFunctionCall", "src": "907:46:1"}, "variables": [{"name": "tail_1", "nodeType": "YulTypedName", "src": "897:6:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "973:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "984:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "969:3:1"}, "nodeType": "YulFunctionCall", "src": "969:18:1"}, {"arguments": [{"name": "value1", "nodeType": "YulIdentifier", "src": "993:6:1"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1009:3:1", "type": "", "value": "160"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1014:1:1", "type": "", "value": "1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "1005:3:1"}, "nodeType": "YulFunctionCall", "src": "1005:11:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1018:1:1", "type": "", "value": "1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1001:3:1"}, "nodeType": "YulFunctionCall", "src": "1001:19:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "989:3:1"}, "nodeType": "YulFunctionCall", "src": "989:32:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "962:6:1"}, "nodeType": "YulFunctionCall", "src": "962:60:1"}, "nodeType": "YulExpressionStatement", "src": "962:60:1"}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1042:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1053:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1038:3:1"}, "nodeType": "YulFunctionCall", "src": "1038:18:1"}, {"arguments": [{"name": "tail_1", "nodeType": "YulIdentifier", "src": "1062:6:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "1070:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "1058:3:1"}, "nodeType": "YulFunctionCall", "src": "1058:22:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1031:6:1"}, "nodeType": "YulFunctionCall", "src": "1031:50:1"}, "nodeType": "YulExpressionStatement", "src": "1031:50:1"}, {"nodeType": "YulAssignment", "src": "1090:41:1", "value": {"arguments": [{"name": "value2", "nodeType": "YulIdentifier", "src": "1116:6:1"}, {"name": "tail_1", "nodeType": "YulIdentifier", "src": "1124:6:1"}], "functionName": {"name": "abi_encode_string", "nodeType": "YulIdentifier", "src": "1098:17:1"}, "nodeType": "YulFunctionCall", "src": "1098:33:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1090:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1151:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1162:2:1", "type": "", "value": "96"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1147:3:1"}, "nodeType": "YulFunctionCall", "src": "1147:18:1"}, {"name": "value3", "nodeType": "YulIdentifier", "src": "1167:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1140:6:1"}, "nodeType": "YulFunctionCall", "src": "1140:34:1"}, "nodeType": "YulExpressionStatement", "src": "1140:34:1"}]}, "name": "abi_encode_tuple_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "797:9:1", "type": ""}, {"name": "value3", "nodeType": "YulTypedName", "src": "808:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "816:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "824:6:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "832:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "843:4:1", "type": ""}], "src": "627:553:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1217:95:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1234:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1241:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1246:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "1237:3:1"}, "nodeType": "YulFunctionCall", "src": "1237:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1227:6:1"}, "nodeType": "YulFunctionCall", "src": "1227:31:1"}, "nodeType": "YulExpressionStatement", "src": "1227:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1274:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1277:4:1", "type": "", "value": "0x41"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1267:6:1"}, "nodeType": "YulFunctionCall", "src": "1267:15:1"}, "nodeType": "YulExpressionStatement", "src": "1267:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1298:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1301:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "1291:6:1"}, "nodeType": "YulFunctionCall", "src": "1291:15:1"}, "nodeType": "YulExpressionStatement", "src": "1291:15:1"}]}, "name": "panic_error_0x41", "nodeType": "YulFunctionDefinition", "src": "1185:127:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1370:666:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1419:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1428:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1431:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "1421:6:1"}, "nodeType": "YulFunctionCall", "src": "1421:12:1"}, "nodeType": "YulExpressionStatement", "src": "1421:12:1"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "1398:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1406:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1394:3:1"}, "nodeType": "YulFunctionCall", "src": "1394:17:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "1413:3:1"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "1390:3:1"}, "nodeType": "YulFunctionCall", "src": "1390:27:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "1383:6:1"}, "nodeType": "YulFunctionCall", "src": "1383:35:1"}, "nodeType": "YulIf", "src": "1380:55:1"}, {"nodeType": "YulVariableDeclaration", "src": "1444:30:1", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "1467:6:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "1454:12:1"}, "nodeType": "YulFunctionCall", "src": "1454:20:1"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "1448:2:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "1483:28:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1493:18:1", "type": "", "value": "0xffffffffffffffff"}, "variables": [{"name": "_2", "nodeType": "YulTypedName", "src": "1487:2:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1534:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "1536:16:1"}, "nodeType": "YulFunctionCall", "src": "1536:18:1"}, "nodeType": "YulExpressionStatement", "src": "1536:18:1"}]}, "condition": {"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "1526:2:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "1530:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "1523:2:1"}, "nodeType": "YulFunctionCall", "src": "1523:10:1"}, "nodeType": "YulIf", "src": "1520:36:1"}, {"nodeType": "YulVariableDeclaration", "src": "1565:17:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1579:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "1575:3:1"}, "nodeType": "YulFunctionCall", "src": "1575:7:1"}, "variables": [{"name": "_3", "nodeType": "YulTypedName", "src": "1569:2:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "1591:23:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1611:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "1605:5:1"}, "nodeType": "YulFunctionCall", "src": "1605:9:1"}, "variables": [{"name": "memPtr", "nodeType": "YulTypedName", "src": "1595:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "1623:71:1", "value": {"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "1645:6:1"}, {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "1669:2:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1673:4:1", "type": "", "value": "0x1f"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1665:3:1"}, "nodeType": "YulFunctionCall", "src": "1665:13:1"}, {"name": "_3", "nodeType": "YulIdentifier", "src": "1680:2:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "1661:3:1"}, "nodeType": "YulFunctionCall", "src": "1661:22:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1685:2:1", "type": "", "value": "63"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1657:3:1"}, "nodeType": "YulFunctionCall", "src": "1657:31:1"}, {"name": "_3", "nodeType": "YulIdentifier", "src": "1690:2:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "1653:3:1"}, "nodeType": "YulFunctionCall", "src": "1653:40:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1641:3:1"}, "nodeType": "YulFunctionCall", "src": "1641:53:1"}, "variables": [{"name": "newFreePtr", "nodeType": "YulTypedName", "src": "1627:10:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1753:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "1755:16:1"}, "nodeType": "YulFunctionCall", "src": "1755:18:1"}, "nodeType": "YulExpressionStatement", "src": "1755:18:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "1712:10:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "1724:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "1709:2:1"}, "nodeType": "YulFunctionCall", "src": "1709:18:1"}, {"arguments": [{"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "1732:10:1"}, {"name": "memPtr", "nodeType": "YulIdentifier", "src": "1744:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "1729:2:1"}, "nodeType": "YulFunctionCall", "src": "1729:22:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "1706:2:1"}, "nodeType": "YulFunctionCall", "src": "1706:46:1"}, "nodeType": "YulIf", "src": "1703:72:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1791:2:1", "type": "", "value": "64"}, {"name": "newFreePtr", "nodeType": "YulIdentifier", "src": "1795:10:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1784:6:1"}, "nodeType": "YulFunctionCall", "src": "1784:22:1"}, "nodeType": "YulExpressionStatement", "src": "1784:22:1"}, {"expression": {"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "1822:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1830:2:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1815:6:1"}, "nodeType": "YulFunctionCall", "src": "1815:18:1"}, "nodeType": "YulExpressionStatement", "src": "1815:18:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1881:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1890:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1893:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "1883:6:1"}, "nodeType": "YulFunctionCall", "src": "1883:12:1"}, "nodeType": "YulExpressionStatement", "src": "1883:12:1"}]}, "condition": {"arguments": [{"arguments": [{"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "1856:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1864:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1852:3:1"}, "nodeType": "YulFunctionCall", "src": "1852:15:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1869:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1848:3:1"}, "nodeType": "YulFunctionCall", "src": "1848:26:1"}, {"name": "end", "nodeType": "YulIdentifier", "src": "1876:3:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "1845:2:1"}, "nodeType": "YulFunctionCall", "src": "1845:35:1"}, "nodeType": "YulIf", "src": "1842:55:1"}, {"expression": {"arguments": [{"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "1923:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1931:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1919:3:1"}, "nodeType": "YulFunctionCall", "src": "1919:17:1"}, {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "1942:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1950:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1938:3:1"}, "nodeType": "YulFunctionCall", "src": "1938:17:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1957:2:1"}], "functionName": {"name": "calldatacopy", "nodeType": "YulIdentifier", "src": "1906:12:1"}, "nodeType": "YulFunctionCall", "src": "1906:54:1"}, "nodeType": "YulExpressionStatement", "src": "1906:54:1"}, {"expression": {"arguments": [{"arguments": [{"arguments": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "1984:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "1992:2:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1980:3:1"}, "nodeType": "YulFunctionCall", "src": "1980:15:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1997:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1976:3:1"}, "nodeType": "YulFunctionCall", "src": "1976:26:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2004:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1969:6:1"}, "nodeType": "YulFunctionCall", "src": "1969:37:1"}, "nodeType": "YulExpressionStatement", "src": "1969:37:1"}, {"nodeType": "YulAssignment", "src": "2015:15:1", "value": {"name": "memPtr", "nodeType": "YulIdentifier", "src": "2024:6:1"}, "variableNames": [{"name": "array", "nodeType": "YulIdentifier", "src": "2015:5:1"}]}]}, "name": "abi_decode_string", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "1344:6:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "1352:3:1", "type": ""}], "returnVariables": [{"name": "array", "nodeType": "YulTypedName", "src": "1360:5:1", "type": ""}], "src": "1317:719:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2165:487:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2211:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2220:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2223:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2213:6:1"}, "nodeType": "YulFunctionCall", "src": "2213:12:1"}, "nodeType": "YulExpressionStatement", "src": "2213:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "2186:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "2195:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "2182:3:1"}, "nodeType": "YulFunctionCall", "src": "2182:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2207:2:1", "type": "", "value": "96"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "2178:3:1"}, "nodeType": "YulFunctionCall", "src": "2178:32:1"}, "nodeType": "YulIf", "src": "2175:52:1"}, {"nodeType": "YulVariableDeclaration", "src": "2236:37:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2263:9:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "2250:12:1"}, "nodeType": "YulFunctionCall", "src": "2250:23:1"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "2240:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "2282:28:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2292:18:1", "type": "", "value": "0xffffffffffffffff"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "2286:2:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2337:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2346:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2349:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2339:6:1"}, "nodeType": "YulFunctionCall", "src": "2339:12:1"}, "nodeType": "YulExpressionStatement", "src": "2339:12:1"}]}, "condition": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "2325:6:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2333:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "2322:2:1"}, "nodeType": "YulFunctionCall", "src": "2322:14:1"}, "nodeType": "YulIf", "src": "2319:34:1"}, {"nodeType": "YulAssignment", "src": "2362:60:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2394:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "2405:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2390:3:1"}, "nodeType": "YulFunctionCall", "src": "2390:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "2414:7:1"}], "functionName": {"name": "abi_decode_string", "nodeType": "YulIdentifier", "src": "2372:17:1"}, "nodeType": "YulFunctionCall", "src": "2372:50:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "2362:6:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "2431:48:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2464:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2475:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2460:3:1"}, "nodeType": "YulFunctionCall", "src": "2460:18:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "2447:12:1"}, "nodeType": "YulFunctionCall", "src": "2447:32:1"}, "variables": [{"name": "offset_1", "nodeType": "YulTypedName", "src": "2435:8:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2508:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2517:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2520:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "2510:6:1"}, "nodeType": "YulFunctionCall", "src": "2510:12:1"}, "nodeType": "YulExpressionStatement", "src": "2510:12:1"}]}, "condition": {"arguments": [{"name": "offset_1", "nodeType": "YulIdentifier", "src": "2494:8:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "2504:2:1"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "2491:2:1"}, "nodeType": "YulFunctionCall", "src": "2491:16:1"}, "nodeType": "YulIf", "src": "2488:36:1"}, {"nodeType": "YulAssignment", "src": "2533:62:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2565:9:1"}, {"name": "offset_1", "nodeType": "YulIdentifier", "src": "2576:8:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2561:3:1"}, "nodeType": "YulFunctionCall", "src": "2561:24:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "2587:7:1"}], "functionName": {"name": "abi_decode_string", "nodeType": "YulIdentifier", "src": "2543:17:1"}, "nodeType": "YulFunctionCall", "src": "2543:52:1"}, "variableNames": [{"name": "value1", "nodeType": "YulIdentifier", "src": "2533:6:1"}]}, {"nodeType": "YulAssignment", "src": "2604:42:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2631:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2642:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2627:3:1"}, "nodeType": "YulFunctionCall", "src": "2627:18:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "2614:12:1"}, "nodeType": "YulFunctionCall", "src": "2614:32:1"}, "variableNames": [{"name": "value2", "nodeType": "YulIdentifier", "src": "2604:6:1"}]}]}, "name": "abi_decode_tuple_t_string_memory_ptrt_string_memory_ptrt_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2115:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "2126:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "2138:6:1", "type": ""}, {"name": "value1", "nodeType": "YulTypedName", "src": "2146:6:1", "type": ""}, {"name": "value2", "nodeType": "YulTypedName", "src": "2154:6:1", "type": ""}], "src": "2041:611:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2758:76:1", "statements": [{"nodeType": "YulAssignment", "src": "2768:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2780:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2791:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "2776:3:1"}, "nodeType": "YulFunctionCall", "src": "2776:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "2768:4:1"}]}, {"expression": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "2810:9:1"}, {"name": "value0", "nodeType": "YulIdentifier", "src": "2821:6:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "2803:6:1"}, "nodeType": "YulFunctionCall", "src": "2803:25:1"}, "nodeType": "YulExpressionStatement", "src": "2803:25:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "2727:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "2738:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "2749:4:1", "type": ""}], "src": "2657:177:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2894:325:1", "statements": [{"nodeType": "YulAssignment", "src": "2904:22:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2918:1:1", "type": "", "value": "1"}, {"name": "data", "nodeType": "YulIdentifier", "src": "2921:4:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "2914:3:1"}, "nodeType": "YulFunctionCall", "src": "2914:12:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "2904:6:1"}]}, {"nodeType": "YulVariableDeclaration", "src": "2935:38:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "2965:4:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2971:1:1", "type": "", "value": "1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "2961:3:1"}, "nodeType": "YulFunctionCall", "src": "2961:12:1"}, "variables": [{"name": "outOfPlaceEncoding", "nodeType": "YulTypedName", "src": "2939:18:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3012:31:1", "statements": [{"nodeType": "YulAssignment", "src": "3014:27:1", "value": {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "3028:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3036:4:1", "type": "", "value": "0x7f"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "3024:3:1"}, "nodeType": "YulFunctionCall", "src": "3024:17:1"}, "variableNames": [{"name": "length", "nodeType": "YulIdentifier", "src": "3014:6:1"}]}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "2992:18:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "2985:6:1"}, "nodeType": "YulFunctionCall", "src": "2985:26:1"}, "nodeType": "YulIf", "src": "2982:61:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3102:111:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3123:1:1", "type": "", "value": "0"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3130:3:1", "type": "", "value": "224"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3135:10:1", "type": "", "value": "0x4e487b71"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "3126:3:1"}, "nodeType": "YulFunctionCall", "src": "3126:20:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3116:6:1"}, "nodeType": "YulFunctionCall", "src": "3116:31:1"}, "nodeType": "YulExpressionStatement", "src": "3116:31:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3167:1:1", "type": "", "value": "4"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3170:4:1", "type": "", "value": "0x22"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3160:6:1"}, "nodeType": "YulFunctionCall", "src": "3160:15:1"}, "nodeType": "YulExpressionStatement", "src": "3160:15:1"}, {"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3195:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3198:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "3188:6:1"}, "nodeType": "YulFunctionCall", "src": "3188:15:1"}, "nodeType": "YulExpressionStatement", "src": "3188:15:1"}]}, "condition": {"arguments": [{"name": "outOfPlaceEncoding", "nodeType": "YulIdentifier", "src": "3058:18:1"}, {"arguments": [{"name": "length", "nodeType": "YulIdentifier", "src": "3081:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3089:2:1", "type": "", "value": "32"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "3078:2:1"}, "nodeType": "YulFunctionCall", "src": "3078:14:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "3055:2:1"}, "nodeType": "YulFunctionCall", "src": "3055:38:1"}, "nodeType": "YulIf", "src": "3052:161:1"}]}, "name": "extract_byte_array_length", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "2874:4:1", "type": ""}], "returnVariables": [{"name": "length", "nodeType": "YulTypedName", "src": "2883:6:1", "type": ""}], "src": "2839:380:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3280:65:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3297:1:1", "type": "", "value": "0"}, {"name": "ptr", "nodeType": "YulIdentifier", "src": "3300:3:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3290:6:1"}, "nodeType": "YulFunctionCall", "src": "3290:14:1"}, "nodeType": "YulExpressionStatement", "src": "3290:14:1"}, {"nodeType": "YulAssignment", "src": "3313:26:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3331:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3334:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "keccak256", "nodeType": "YulIdentifier", "src": "3321:9:1"}, "nodeType": "YulFunctionCall", "src": "3321:18:1"}, "variableNames": [{"name": "data", "nodeType": "YulIdentifier", "src": "3313:4:1"}]}]}, "name": "array_dataslot_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nodeType": "YulTypedName", "src": "3263:3:1", "type": ""}], "returnVariables": [{"name": "data", "nodeType": "YulTypedName", "src": "3271:4:1", "type": ""}], "src": "3224:121:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3431:464:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3464:425:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "3478:11:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3488:1:1", "type": "", "value": "0"}, "variables": [{"name": "_1", "nodeType": "YulTypedName", "src": "3482:2:1", "type": ""}]}, {"expression": {"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "3509:2:1"}, {"name": "array", "nodeType": "YulIdentifier", "src": "3513:5:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "3502:6:1"}, "nodeType": "YulFunctionCall", "src": "3502:17:1"}, "nodeType": "YulExpressionStatement", "src": "3502:17:1"}, {"nodeType": "YulVariableDeclaration", "src": "3532:31:1", "value": {"arguments": [{"name": "_1", "nodeType": "YulIdentifier", "src": "3554:2:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3558:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "keccak256", "nodeType": "YulIdentifier", "src": "3544:9:1"}, "nodeType": "YulFunctionCall", "src": "3544:19:1"}, "variables": [{"name": "data", "nodeType": "YulTypedName", "src": "3536:4:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "3576:57:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "3599:4:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3609:1:1", "type": "", "value": "5"}, {"arguments": [{"name": "startIndex", "nodeType": "YulIdentifier", "src": "3616:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3628:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3612:3:1"}, "nodeType": "YulFunctionCall", "src": "3612:19:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "3605:3:1"}, "nodeType": "YulFunctionCall", "src": "3605:27:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3595:3:1"}, "nodeType": "YulFunctionCall", "src": "3595:38:1"}, "variables": [{"name": "deleteStart", "nodeType": "YulTypedName", "src": "3580:11:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3670:23:1", "statements": [{"nodeType": "YulAssignment", "src": "3672:19:1", "value": {"name": "data", "nodeType": "YulIdentifier", "src": "3687:4:1"}, "variableNames": [{"name": "deleteStart", "nodeType": "YulIdentifier", "src": "3672:11:1"}]}]}, "condition": {"arguments": [{"name": "startIndex", "nodeType": "YulIdentifier", "src": "3652:10:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3664:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "3649:2:1"}, "nodeType": "YulFunctionCall", "src": "3649:20:1"}, "nodeType": "YulIf", "src": "3646:47:1"}, {"nodeType": "YulVariableDeclaration", "src": "3706:41:1", "value": {"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "3720:4:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3730:1:1", "type": "", "value": "5"}, {"arguments": [{"name": "len", "nodeType": "YulIdentifier", "src": "3737:3:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3742:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3733:3:1"}, "nodeType": "YulFunctionCall", "src": "3733:12:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "3726:3:1"}, "nodeType": "YulFunctionCall", "src": "3726:20:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3716:3:1"}, "nodeType": "YulFunctionCall", "src": "3716:31:1"}, "variables": [{"name": "_2", "nodeType": "YulTypedName", "src": "3710:2:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "3760:24:1", "value": {"name": "deleteStart", "nodeType": "YulIdentifier", "src": "3773:11:1"}, "variables": [{"name": "start", "nodeType": "YulTypedName", "src": "3764:5:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3858:21:1", "statements": [{"expression": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "3867:5:1"}, {"name": "_1", "nodeType": "YulIdentifier", "src": "3874:2:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "3860:6:1"}, "nodeType": "YulFunctionCall", "src": "3860:17:1"}, "nodeType": "YulExpressionStatement", "src": "3860:17:1"}]}, "condition": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "3808:5:1"}, {"name": "_2", "nodeType": "YulIdentifier", "src": "3815:2:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "3805:2:1"}, "nodeType": "YulFunctionCall", "src": "3805:13:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3819:26:1", "statements": [{"nodeType": "YulAssignment", "src": "3821:22:1", "value": {"arguments": [{"name": "start", "nodeType": "YulIdentifier", "src": "3834:5:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3841:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "3830:3:1"}, "nodeType": "YulFunctionCall", "src": "3830:13:1"}, "variableNames": [{"name": "start", "nodeType": "YulIdentifier", "src": "3821:5:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3801:3:1", "statements": []}, "src": "3797:82:1"}]}, "condition": {"arguments": [{"name": "len", "nodeType": "YulIdentifier", "src": "3447:3:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3452:2:1", "type": "", "value": "31"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "3444:2:1"}, "nodeType": "YulFunctionCall", "src": "3444:11:1"}, "nodeType": "YulIf", "src": "3441:448:1"}]}, "name": "clean_up_bytearray_end_slots_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "array", "nodeType": "YulTypedName", "src": "3403:5:1", "type": ""}, {"name": "len", "nodeType": "YulTypedName", "src": "3410:3:1", "type": ""}, {"name": "startIndex", "nodeType": "YulTypedName", "src": "3415:10:1", "type": ""}], "src": "3350:545:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3985:81:1", "statements": [{"nodeType": "YulAssignment", "src": "3995:65:1", "value": {"arguments": [{"arguments": [{"name": "data", "nodeType": "YulIdentifier", "src": "4010:4:1"}, {"arguments": [{"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4028:1:1", "type": "", "value": "3"}, {"name": "len", "nodeType": "YulIdentifier", "src": "4031:3:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "4024:3:1"}, "nodeType": "YulFunctionCall", "src": "4024:11:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4041:1:1", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "4037:3:1"}, "nodeType": "YulFunctionCall", "src": "4037:6:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "4020:3:1"}, "nodeType": "YulFunctionCall", "src": "4020:24:1"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "4016:3:1"}, "nodeType": "YulFunctionCall", "src": "4016:29:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "4006:3:1"}, "nodeType": "YulFunctionCall", "src": "4006:40:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4052:1:1", "type": "", "value": "1"}, {"name": "len", "nodeType": "YulIdentifier", "src": "4055:3:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "4048:3:1"}, "nodeType": "YulFunctionCall", "src": "4048:11:1"}], "functionName": {"name": "or", "nodeType": "YulIdentifier", "src": "4003:2:1"}, "nodeType": "YulFunctionCall", "src": "4003:57:1"}, "variableNames": [{"name": "used", "nodeType": "YulIdentifier", "src": "3995:4:1"}]}]}, "name": "extract_used_part_and_set_length_of_short_byte_array", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "data", "nodeType": "YulTypedName", "src": "3962:4:1", "type": ""}, {"name": "len", "nodeType": "YulTypedName", "src": "3968:3:1", "type": ""}], "returnVariables": [{"name": "used", "nodeType": "YulTypedName", "src": "3976:4:1", "type": ""}], "src": "3900:166:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4167:1256:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "4177:24:1", "value": {"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "4197:3:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "4191:5:1"}, "nodeType": "YulFunctionCall", "src": "4191:10:1"}, "variables": [{"name": "newLen", "nodeType": "YulTypedName", "src": "4181:6:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4244:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x41", "nodeType": "YulIdentifier", "src": "4246:16:1"}, "nodeType": "YulFunctionCall", "src": "4246:18:1"}, "nodeType": "YulExpressionStatement", "src": "4246:18:1"}]}, "condition": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "4216:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4224:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4213:2:1"}, "nodeType": "YulFunctionCall", "src": "4213:30:1"}, "nodeType": "YulIf", "src": "4210:56:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "4319:4:1"}, {"arguments": [{"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "4357:4:1"}], "functionName": {"name": "sload", "nodeType": "YulIdentifier", "src": "4351:5:1"}, "nodeType": "YulFunctionCall", "src": "4351:11:1"}], "functionName": {"name": "extract_byte_array_length", "nodeType": "YulIdentifier", "src": "4325:25:1"}, "nodeType": "YulFunctionCall", "src": "4325:38:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "4365:6:1"}], "functionName": {"name": "clean_up_bytearray_end_slots_string_storage", "nodeType": "YulIdentifier", "src": "4275:43:1"}, "nodeType": "YulFunctionCall", "src": "4275:97:1"}, "nodeType": "YulExpressionStatement", "src": "4275:97:1"}, {"nodeType": "YulVariableDeclaration", "src": "4381:18:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4398:1:1", "type": "", "value": "0"}, "variables": [{"name": "srcOffset", "nodeType": "YulTypedName", "src": "4385:9:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4408:23:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4427:4:1", "type": "", "value": "0x20"}, "variables": [{"name": "srcOffset_1", "nodeType": "YulTypedName", "src": "4412:11:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "4440:24:1", "value": {"name": "srcOffset_1", "nodeType": "YulIdentifier", "src": "4453:11:1"}, "variableNames": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "4440:9:1"}]}, {"cases": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4510:656:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "4524:35:1", "value": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "4543:6:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4555:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "4551:3:1"}, "nodeType": "YulFunctionCall", "src": "4551:7:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "4539:3:1"}, "nodeType": "YulFunctionCall", "src": "4539:20:1"}, "variables": [{"name": "loopEnd", "nodeType": "YulTypedName", "src": "4528:7:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4572:49:1", "value": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "4616:4:1"}], "functionName": {"name": "array_dataslot_string_storage", "nodeType": "YulIdentifier", "src": "4586:29:1"}, "nodeType": "YulFunctionCall", "src": "4586:35:1"}, "variables": [{"name": "dstPtr", "nodeType": "YulTypedName", "src": "4576:6:1", "type": ""}]}, {"nodeType": "YulVariableDeclaration", "src": "4634:10:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4643:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nodeType": "YulTypedName", "src": "4638:1:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4721:172:1", "statements": [{"expression": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "4746:6:1"}, {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "4764:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "4769:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4760:3:1"}, "nodeType": "YulFunctionCall", "src": "4760:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "4754:5:1"}, "nodeType": "YulFunctionCall", "src": "4754:26:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "4739:6:1"}, "nodeType": "YulFunctionCall", "src": "4739:42:1"}, "nodeType": "YulExpressionStatement", "src": "4739:42:1"}, {"nodeType": "YulAssignment", "src": "4798:24:1", "value": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "4812:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4820:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4808:3:1"}, "nodeType": "YulFunctionCall", "src": "4808:14:1"}, "variableNames": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "4798:6:1"}]}, {"nodeType": "YulAssignment", "src": "4839:40:1", "value": {"arguments": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "4856:9:1"}, {"name": "srcOffset_1", "nodeType": "YulIdentifier", "src": "4867:11:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4852:3:1"}, "nodeType": "YulFunctionCall", "src": "4852:27:1"}, "variableNames": [{"name": "srcOffset", "nodeType": "YulIdentifier", "src": "4839:9:1"}]}]}, "condition": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "4668:1:1"}, {"name": "loopEnd", "nodeType": "YulIdentifier", "src": "4671:7:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "4665:2:1"}, "nodeType": "YulFunctionCall", "src": "4665:14:1"}, "nodeType": "YulForLoop", "post": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4680:28:1", "statements": [{"nodeType": "YulAssignment", "src": "4682:24:1", "value": {"arguments": [{"name": "i", "nodeType": "YulIdentifier", "src": "4691:1:1"}, {"name": "srcOffset_1", "nodeType": "YulIdentifier", "src": "4694:11:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4687:3:1"}, "nodeType": "YulFunctionCall", "src": "4687:19:1"}, "variableNames": [{"name": "i", "nodeType": "YulIdentifier", "src": "4682:1:1"}]}]}, "pre": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4661:3:1", "statements": []}, "src": "4657:236:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4941:166:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "4959:43:1", "value": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "4986:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "4991:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "4982:3:1"}, "nodeType": "YulFunctionCall", "src": "4982:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "4976:5:1"}, "nodeType": "YulFunctionCall", "src": "4976:26:1"}, "variables": [{"name": "lastValue", "nodeType": "YulTypedName", "src": "4963:9:1", "type": ""}]}, {"expression": {"arguments": [{"name": "dstPtr", "nodeType": "YulIdentifier", "src": "5026:6:1"}, {"arguments": [{"name": "lastValue", "nodeType": "YulIdentifier", "src": "5038:9:1"}, {"arguments": [{"arguments": [{"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5065:1:1", "type": "", "value": "3"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "5068:6:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "5061:3:1"}, "nodeType": "YulFunctionCall", "src": "5061:14:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5077:3:1", "type": "", "value": "248"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "5057:3:1"}, "nodeType": "YulFunctionCall", "src": "5057:24:1"}, {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5087:1:1", "type": "", "value": "0"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "5083:3:1"}, "nodeType": "YulFunctionCall", "src": "5083:6:1"}], "functionName": {"name": "shr", "nodeType": "YulIdentifier", "src": "5053:3:1"}, "nodeType": "YulFunctionCall", "src": "5053:37:1"}], "functionName": {"name": "not", "nodeType": "YulIdentifier", "src": "5049:3:1"}, "nodeType": "YulFunctionCall", "src": "5049:42:1"}], "functionName": {"name": "and", "nodeType": "YulIdentifier", "src": "5034:3:1"}, "nodeType": "YulFunctionCall", "src": "5034:58:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "5019:6:1"}, "nodeType": "YulFunctionCall", "src": "5019:74:1"}, "nodeType": "YulExpressionStatement", "src": "5019:74:1"}]}, "condition": {"arguments": [{"name": "loopEnd", "nodeType": "YulIdentifier", "src": "4912:7:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "4921:6:1"}], "functionName": {"name": "lt", "nodeType": "YulIdentifier", "src": "4909:2:1"}, "nodeType": "YulFunctionCall", "src": "4909:19:1"}, "nodeType": "YulIf", "src": "4906:201:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "5127:4:1"}, {"arguments": [{"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5141:1:1", "type": "", "value": "1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "5144:6:1"}], "functionName": {"name": "shl", "nodeType": "YulIdentifier", "src": "5137:3:1"}, "nodeType": "YulFunctionCall", "src": "5137:14:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5153:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5133:3:1"}, "nodeType": "YulFunctionCall", "src": "5133:22:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "5120:6:1"}, "nodeType": "YulFunctionCall", "src": "5120:36:1"}, "nodeType": "YulExpressionStatement", "src": "5120:36:1"}]}, "nodeType": "YulCase", "src": "4503:663:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4508:1:1", "type": "", "value": "1"}}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5183:234:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "5197:14:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5210:1:1", "type": "", "value": "0"}, "variables": [{"name": "value", "nodeType": "YulTypedName", "src": "5201:5:1", "type": ""}]}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5246:67:1", "statements": [{"nodeType": "YulAssignment", "src": "5264:35:1", "value": {"arguments": [{"arguments": [{"name": "src", "nodeType": "YulIdentifier", "src": "5283:3:1"}, {"name": "srcOffset", "nodeType": "YulIdentifier", "src": "5288:9:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "5279:3:1"}, "nodeType": "YulFunctionCall", "src": "5279:19:1"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "5273:5:1"}, "nodeType": "YulFunctionCall", "src": "5273:26:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "5264:5:1"}]}]}, "condition": {"name": "newLen", "nodeType": "YulIdentifier", "src": "5227:6:1"}, "nodeType": "YulIf", "src": "5224:89:1"}, {"expression": {"arguments": [{"name": "slot", "nodeType": "YulIdentifier", "src": "5333:4:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "5392:5:1"}, {"name": "newLen", "nodeType": "YulIdentifier", "src": "5399:6:1"}], "functionName": {"name": "extract_used_part_and_set_length_of_short_byte_array", "nodeType": "YulIdentifier", "src": "5339:52:1"}, "nodeType": "YulFunctionCall", "src": "5339:67:1"}], "functionName": {"name": "sstore", "nodeType": "YulIdentifier", "src": "5326:6:1"}, "nodeType": "YulFunctionCall", "src": "5326:81:1"}, "nodeType": "YulExpressionStatement", "src": "5326:81:1"}]}, "nodeType": "YulCase", "src": "5175:242:1", "value": "default"}], "expression": {"arguments": [{"name": "newLen", "nodeType": "YulIdentifier", "src": "4483:6:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4491:2:1", "type": "", "value": "31"}], "functionName": {"name": "gt", "nodeType": "YulIdentifier", "src": "4480:2:1"}, "nodeType": "YulFunctionCall", "src": "4480:14:1"}, "nodeType": "YulSwitch", "src": "4473:944:1"}]}, "name": "copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "slot", "nodeType": "YulTypedName", "src": "4152:4:1", "type": ""}, {"name": "src", "nodeType": "YulTypedName", "src": "4158:3:1", "type": ""}], "src": "4071:1352:1"}]}, "contents": "{\n    { }\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0\n    {\n        if slt(sub(dataEnd, headStart), 32) { revert(0, 0) }\n        value0 := calldataload(headStart)\n    }\n    function abi_encode_string(value, pos) -> end\n    {\n        let length := mload(value)\n        mstore(pos, length)\n        let i := 0\n        for { } lt(i, length) { i := add(i, 0x20) }\n        {\n            let _1 := 0x20\n            mstore(add(add(pos, i), _1), mload(add(add(value, i), _1)))\n        }\n        mstore(add(add(pos, length), 0x20), 0)\n        end := add(add(pos, and(add(length, 31), not(31))), 0x20)\n    }\n    function abi_encode_tuple_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__to_t_string_memory_ptr_t_address_t_string_memory_ptr_t_uint256__fromStack_reversed(headStart, value3, value2, value1, value0) -> tail\n    {\n        mstore(headStart, 128)\n        let tail_1 := abi_encode_string(value0, add(headStart, 128))\n        mstore(add(headStart, 32), and(value1, sub(shl(160, 1), 1)))\n        mstore(add(headStart, 64), sub(tail_1, headStart))\n        tail := abi_encode_string(value2, tail_1)\n        mstore(add(headStart, 96), value3)\n    }\n    function panic_error_0x41()\n    {\n        mstore(0, shl(224, 0x4e487b71))\n        mstore(4, 0x41)\n        revert(0, 0x24)\n    }\n    function abi_decode_string(offset, end) -> array\n    {\n        if iszero(slt(add(offset, 0x1f), end)) { revert(0, 0) }\n        let _1 := calldataload(offset)\n        let _2 := 0xffffffffffffffff\n        if gt(_1, _2) { panic_error_0x41() }\n        let _3 := not(31)\n        let memPtr := mload(64)\n        let newFreePtr := add(memPtr, and(add(and(add(_1, 0x1f), _3), 63), _3))\n        if or(gt(newFreePtr, _2), lt(newFreePtr, memPtr)) { panic_error_0x41() }\n        mstore(64, newFreePtr)\n        mstore(memPtr, _1)\n        if gt(add(add(offset, _1), 0x20), end) { revert(0, 0) }\n        calldatacopy(add(memPtr, 0x20), add(offset, 0x20), _1)\n        mstore(add(add(memPtr, _1), 0x20), 0)\n        array := memPtr\n    }\n    function abi_decode_tuple_t_string_memory_ptrt_string_memory_ptrt_uint256(headStart, dataEnd) -> value0, value1, value2\n    {\n        if slt(sub(dataEnd, headStart), 96) { revert(0, 0) }\n        let offset := calldataload(headStart)\n        let _1 := 0xffffffffffffffff\n        if gt(offset, _1) { revert(0, 0) }\n        value0 := abi_decode_string(add(headStart, offset), dataEnd)\n        let offset_1 := calldataload(add(headStart, 32))\n        if gt(offset_1, _1) { revert(0, 0) }\n        value1 := abi_decode_string(add(headStart, offset_1), dataEnd)\n        value2 := calldataload(add(headStart, 64))\n    }\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart, value0) -> tail\n    {\n        tail := add(headStart, 32)\n        mstore(headStart, value0)\n    }\n    function extract_byte_array_length(data) -> length\n    {\n        length := shr(1, data)\n        let outOfPlaceEncoding := and(data, 1)\n        if iszero(outOfPlaceEncoding) { length := and(length, 0x7f) }\n        if eq(outOfPlaceEncoding, lt(length, 32))\n        {\n            mstore(0, shl(224, 0x4e487b71))\n            mstore(4, 0x22)\n            revert(0, 0x24)\n        }\n    }\n    function array_dataslot_string_storage(ptr) -> data\n    {\n        mstore(0, ptr)\n        data := keccak256(0, 0x20)\n    }\n    function clean_up_bytearray_end_slots_string_storage(array, len, startIndex)\n    {\n        if gt(len, 31)\n        {\n            let _1 := 0\n            mstore(_1, array)\n            let data := keccak256(_1, 0x20)\n            let deleteStart := add(data, shr(5, add(startIndex, 31)))\n            if lt(startIndex, 0x20) { deleteStart := data }\n            let _2 := add(data, shr(5, add(len, 31)))\n            let start := deleteStart\n            for { } lt(start, _2) { start := add(start, 1) }\n            { sstore(start, _1) }\n        }\n    }\n    function extract_used_part_and_set_length_of_short_byte_array(data, len) -> used\n    {\n        used := or(and(data, not(shr(shl(3, len), not(0)))), shl(1, len))\n    }\n    function copy_byte_array_to_storage_from_t_string_memory_ptr_to_t_string_storage(slot, src)\n    {\n        let newLen := mload(src)\n        if gt(newLen, 0xffffffffffffffff) { panic_error_0x41() }\n        clean_up_bytearray_end_slots_string_storage(slot, extract_byte_array_length(sload(slot)), newLen)\n        let srcOffset := 0\n        let srcOffset_1 := 0x20\n        srcOffset := srcOffset_1\n        switch gt(newLen, 31)\n        case 1 {\n            let loopEnd := and(newLen, not(31))\n            let dstPtr := array_dataslot_string_storage(slot)\n            let i := 0\n            for { } lt(i, loopEnd) { i := add(i, srcOffset_1) }\n            {\n                sstore(dstPtr, mload(add(src, srcOffset)))\n                dstPtr := add(dstPtr, 1)\n                srcOffset := add(srcOffset, srcOffset_1)\n            }\n            if lt(loopEnd, newLen)\n            {\n                let lastValue := mload(add(src, srcOffset))\n                sstore(dstPtr, and(lastValue, not(shr(and(shl(3, newLen), 248), not(0)))))\n            }\n            sstore(slot, add(shl(1, newLen), 1))\n        }\n        default {\n            let value := 0\n            if newLen\n            {\n                value := mload(add(src, srcOffset))\n            }\n            sstore(slot, extract_used_part_and_set_length_of_short_byte_array(value, newLen))\n        }\n    }\n}", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "sourceMap": "61:491:0:-:0;;;265:16;;;;;;;;;;61:491;;;;;;", "deployedSourceMap": "61:491:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;210:19;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;289:156;;;;;;:::i;:::-;;:::i;:::-;;453:96;502:7;529:12;453:96;;2803:25:1;;;2791:2;2776:18;453:96:0;2657:177:1;210:19:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;210:19:0;;;;;;;;;;;-1:-1:-1;;;;;210:19:0;;;;;;-1:-1:-1;210:19:0;-1:-1:-1;210:19:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;289:156::-;398:38;;;;;;;;;;;411:10;398:38;;;;;;;;;;;;;;;;387:5;:50;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;387:50:0;;;;;;;;;-1:-1:-1;;;;;;387:50:0;-1:-1:-1;;;;;387:50:0;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;289:156;;;:::o;14:180:1:-;73:6;126:2;114:9;105:7;101:23;97:32;94:52;;;142:1;139;132:12;94:52;-1:-1:-1;165:23:1;;14:180;-1:-1:-1;14:180:1:o;199:423::-;241:3;279:5;273:12;306:6;301:3;294:19;331:1;341:162;355:6;352:1;349:13;341:162;;;417:4;473:13;;;469:22;;463:29;445:11;;;441:20;;434:59;370:12;341:162;;;345:3;548:1;541:4;532:6;527:3;523:16;519:27;512:38;611:4;604:2;600:7;595:2;587:6;583:15;579:29;574:3;570:39;566:50;559:57;;;199:423;;;;:::o;627:553::-;880:3;869:9;862:22;843:4;907:46;948:3;937:9;933:19;925:6;907:46;:::i;:::-;-1:-1:-1;;;;;989:32:1;;984:2;969:18;;962:60;1058:22;;;1053:2;1038:18;;1031:50;1098:33;1062:6;1116;1098:33;:::i;:::-;1090:41;;;1167:6;1162:2;1151:9;1147:18;1140:34;627:553;;;;;;;:::o;1185:127::-;1246:10;1241:3;1237:20;1234:1;1227:31;1277:4;1274:1;1267:15;1301:4;1298:1;1291:15;1317:719;1360:5;1413:3;1406:4;1398:6;1394:17;1390:27;1380:55;;1431:1;1428;1421:12;1380:55;1467:6;1454:20;1493:18;1530:2;1526;1523:10;1520:36;;;1536:18;;:::i;:::-;1611:2;1605:9;1579:2;1665:13;;-1:-1:-1;;1661:22:1;;;1685:2;1657:31;1653:40;1641:53;;;1709:18;;;1729:22;;;1706:46;1703:72;;;1755:18;;:::i;:::-;1795:10;1791:2;1784:22;1830:2;1822:6;1815:18;1876:3;1869:4;1864:2;1856:6;1852:15;1848:26;1845:35;1842:55;;;1893:1;1890;1883:12;1842:55;1957:2;1950:4;1942:6;1938:17;1931:4;1923:6;1919:17;1906:54;2004:1;1997:4;1992:2;1984:6;1980:15;1976:26;1969:37;2024:6;2015:15;;;;;;1317:719;;;;:::o;2041:611::-;2138:6;2146;2154;2207:2;2195:9;2186:7;2182:23;2178:32;2175:52;;;2223:1;2220;2213:12;2175:52;2263:9;2250:23;2292:18;2333:2;2325:6;2322:14;2319:34;;;2349:1;2346;2339:12;2319:34;2372:50;2414:7;2405:6;2394:9;2390:22;2372:50;:::i;:::-;2362:60;;2475:2;2464:9;2460:18;2447:32;2431:48;;2504:2;2494:8;2491:16;2488:36;;;2520:1;2517;2510:12;2488:36;;2543:52;2587:7;2576:8;2565:9;2561:24;2543:52;:::i;:::-;2533:62;;;2642:2;2631:9;2627:18;2614:32;2604:42;;2041:611;;;;;:::o;2839:380::-;2918:1;2914:12;;;;2961;;;2982:61;;3036:4;3028:6;3024:17;3014:27;;2982:61;3089:2;3081:6;3078:14;3058:18;3055:38;3052:161;;3135:10;3130:3;3126:20;3123:1;3116:31;3170:4;3167:1;3160:15;3198:4;3195:1;3188:15;3052:161;;2839:380;;;:::o;3350:545::-;3452:2;3447:3;3444:11;3441:448;;;3488:1;3513:5;3509:2;3502:17;3558:4;3554:2;3544:19;3628:2;3616:10;3612:19;3609:1;3605:27;3599:4;3595:38;3664:4;3652:10;3649:20;3646:47;;;-1:-1:-1;3687:4:1;3646:47;3742:2;3737:3;3733:12;3730:1;3726:20;3720:4;3716:31;3706:41;;3797:82;3815:2;3808:5;3805:13;3797:82;;;3860:17;;;3841:1;3830:13;3797:82;;;3801:3;;;3441:448;3350:545;;;:::o;4071:1352::-;4197:3;4191:10;4224:18;4216:6;4213:30;4210:56;;;4246:18;;:::i;:::-;4275:97;4365:6;4325:38;4357:4;4351:11;4325:38;:::i;:::-;4319:4;4275:97;:::i;:::-;4427:4;;4491:2;4480:14;;4508:1;4503:663;;;;5210:1;5227:6;5224:89;;;-1:-1:-1;5279:19:1;;;5273:26;5224:89;-1:-1:-1;;4028:1:1;4024:11;;;4020:24;4016:29;4006:40;4052:1;4048:11;;;4003:57;5326:81;;4473:944;;4503:663;3297:1;3290:14;;;3334:4;3321:18;;-1:-1:-1;;4539:20:1;;;4657:236;4671:7;4668:1;4665:14;4657:236;;;4760:19;;;4754:26;4739:42;;4852:27;;;;4820:1;4808:14;;;;4687:19;;4657:236;;;4661:3;4921:6;4912:7;4909:19;4906:201;;;4982:19;;;4976:26;-1:-1:-1;;5065:1:1;5061:14;;;5077:3;5057:24;5053:37;5049:42;5034:58;5019:74;;4906:201;-1:-1:-1;;;;;5153:1:1;5137:14;;;5133:22;5120:36;;-1:-1:-1;4071:1352:1:o", "source": "// SPDX-License-Identifier: MIT\r\npragma solidity ^0.8.19;\r\n\r\ncontract SongRegistry {\r\n    struct Song {\r\n        string title;\r\n        address owner;\r\n        string url;\r\n        uint256 price;\r\n    }\r\n\r\n    Song[] public songs;\r\n\r\n    // Simple constructor\r\n    constructor() {}\r\n\r\n    function registerSong(string memory _title, string memory _url, uint256 _price) public {\r\n        songs.push(Song(_title, msg.sender, _url, _price));\r\n    }\r\n\r\n    function getNumberOfSongs() public view returns (uint256) {\r\n        return songs.length;\r\n    }\r\n}", "sourcePath": "C:\\Users\\<USER>\\Downloads\\SongRegistry-SmartContract\\thirdProject\\contracts\\songRegistry.sol", "ast": {"absolutePath": "project:/contracts/songRegistry.sol", "exportedSymbols": {"SongRegistry": [50]}, "id": 51, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".19"], "nodeType": "PragmaDirective", "src": "33:24:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "SongRegistry", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 50, "linearizedBaseContracts": [50], "name": "SongRegistry", "nameLocation": "70:12:0", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "SongRegistry.Song", "id": 10, "members": [{"constant": false, "id": 3, "mutability": "mutable", "name": "title", "nameLocation": "120:5:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "113:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 2, "name": "string", "nodeType": "ElementaryTypeName", "src": "113:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 5, "mutability": "mutable", "name": "owner", "nameLocation": "144:5:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "136:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4, "name": "address", "nodeType": "ElementaryTypeName", "src": "136:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 7, "mutability": "mutable", "name": "url", "nameLocation": "167:3:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "160:10:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 6, "name": "string", "nodeType": "ElementaryTypeName", "src": "160:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 9, "mutability": "mutable", "name": "price", "nameLocation": "189:5:0", "nodeType": "VariableDeclaration", "scope": 10, "src": "181:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 8, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "181:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "name": "Song", "nameLocation": "97:4:0", "nodeType": "StructDefinition", "scope": 50, "src": "90:112:0", "visibility": "public"}, {"constant": false, "functionSelector": "304cff30", "id": 14, "mutability": "mutable", "name": "songs", "nameLocation": "224:5:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "210:19:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage", "typeString": "struct SongRegistry.Song[]"}, "typeName": {"baseType": {"id": 12, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 11, "name": "Song", "nameLocations": ["210:4:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 10, "src": "210:4:0"}, "referencedDeclaration": 10, "src": "210:4:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Song_$10_storage_ptr", "typeString": "struct SongRegistry.Song"}}, "id": 13, "nodeType": "ArrayTypeName", "src": "210:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage_ptr", "typeString": "struct SongRegistry.Song[]"}}, "visibility": "public"}, {"body": {"id": 17, "nodeType": "Block", "src": "279:2:0", "statements": []}, "id": 18, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 15, "nodeType": "ParameterList", "parameters": [], "src": "276:2:0"}, "returnParameters": {"id": 16, "nodeType": "ParameterList", "parameters": [], "src": "279:0:0"}, "scope": 50, "src": "265:16:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 39, "nodeType": "Block", "src": "376:69:0", "statements": [{"expression": {"arguments": [{"arguments": [{"id": 31, "name": "_title", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 20, "src": "403:6:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 32, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4294967281, "src": "411:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 33, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "415:6:0", "memberName": "sender", "nodeType": "MemberAccess", "src": "411:10:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 34, "name": "_url", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 22, "src": "423:4:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 35, "name": "_price", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 24, "src": "429:6:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 30, "name": "Song", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 10, "src": "398:4:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Song_$10_storage_ptr_$", "typeString": "type(struct SongRegistry.Song storage pointer)"}}, "id": 36, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "398:38:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Song_$10_memory_ptr", "typeString": "struct SongRegistry.Song memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_struct$_Song_$10_memory_ptr", "typeString": "struct SongRegistry.Song memory"}], "expression": {"id": 27, "name": "songs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "387:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage", "typeString": "struct SongRegistry.Song storage ref[] storage ref"}}, "id": 29, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "393:4:0", "memberName": "push", "nodeType": "MemberAccess", "src": "387:10:0", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_struct$_Song_$10_storage_$dyn_storage_ptr_$_t_struct$_Song_$10_storage_$returns$__$attached_to$_t_array$_t_struct$_Song_$10_storage_$dyn_storage_ptr_$", "typeString": "function (struct SongRegistry.Song storage ref[] storage pointer,struct SongRegistry.Song storage ref)"}}, "id": 37, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "387:50:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 38, "nodeType": "ExpressionStatement", "src": "387:50:0"}]}, "functionSelector": "30753c7a", "id": 40, "implemented": true, "kind": "function", "modifiers": [], "name": "registerSong", "nameLocation": "298:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 25, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "mutability": "mutable", "name": "_title", "nameLocation": "325:6:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "311:20:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 19, "name": "string", "nodeType": "ElementaryTypeName", "src": "311:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 22, "mutability": "mutable", "name": "_url", "nameLocation": "347:4:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "333:18:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 21, "name": "string", "nodeType": "ElementaryTypeName", "src": "333:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 24, "mutability": "mutable", "name": "_price", "nameLocation": "361:6:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "353:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 23, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "353:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "310:58:0"}, "returnParameters": {"id": 26, "nodeType": "ParameterList", "parameters": [], "src": "376:0:0"}, "scope": 50, "src": "289:156:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 48, "nodeType": "Block", "src": "511:38:0", "statements": [{"expression": {"expression": {"id": 45, "name": "songs", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "529:5:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Song_$10_storage_$dyn_storage", "typeString": "struct SongRegistry.Song storage ref[] storage ref"}}, "id": 46, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "535:6:0", "memberName": "length", "nodeType": "MemberAccess", "src": "529:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 44, "id": 47, "nodeType": "Return", "src": "522:19:0"}]}, "functionSelector": "a8ec42d8", "id": 49, "implemented": true, "kind": "function", "modifiers": [], "name": "getNumberOfSongs", "nameLocation": "462:16:0", "nodeType": "FunctionDefinition", "parameters": {"id": 41, "nodeType": "ParameterList", "parameters": [], "src": "478:2:0"}, "returnParameters": {"id": 44, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 49, "src": "502:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 42, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "502:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "501:9:0"}, "scope": 50, "src": "453:96:0", "stateMutability": "view", "virtual": false, "visibility": "public"}], "scope": 51, "src": "61:491:0", "usedErrors": []}], "src": "33:519:0"}, "compiler": {"name": "solc", "version": "0.8.19+commit.7dd6d404.Emscripten.clang"}, "networks": {"5777": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0x28a13be2f2b67b245fa5faf3ed91e2a28d8bd3cd1c3a7416c02736fa3b64727b"}}, "schemaVersion": "3.4.16", "updatedAt": "2025-06-17T07:43:24.461Z", "networkType": "ethereum", "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}